<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新闻爬取工具</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/themes.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- 标题栏 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="material-icons">newspaper</i>
                    新闻爬取工具
                </h1>
                <div class="header-actions">
                    <div class="status-indicators">
                        <div class="status-item" id="api-status">
                            <i class="material-icons">warning</i>
                            <span>API未设置</span>
                        </div>
                        <div class="status-item" id="url-status">
                            <i class="material-icons">link</i>
                            <span>URL: 0</span>
                        </div>
                    </div>
                    <button class="btn-icon" id="settings-btn" title="设置">
                        <i class="material-icons">settings</i>
                    </button>
                    <button class="btn-icon" id="theme-toggle" title="切换主题">
                        <i class="material-icons">light_mode</i>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 文章选择区域 -->
            <div class="articles-section">
                <div class="articles-header">
                    <h3 class="section-title">
                        <i class="material-icons">article</i>
                        文章列表
                    </h3>
                    <div class="articles-controls">
                        <div class="articles-actions">
                            <button id="select-all-articles" class="btn-secondary">全选</button>
                            <button id="deselect-all-articles" class="btn-secondary">取消全选</button>
                            <span class="selection-count">已选择: <span id="selected-count">0</span> 篇</span>
                        </div>
                        <button id="start-processing" class="btn-primary" disabled>
                            <i class="material-icons">play_arrow</i>
                            开始处理
                        </button>
                    </div>
                </div>
                
                <div class="articles-search-container">
                    <div class="search-box">
                        <input type="text" id="article-search" placeholder="搜索文章标题..." class="input-field">
                        <i class="material-icons">search</i>
                    </div>
                </div>
                
                <div id="articles-list" class="articles-list-main">
                    <div class="empty-articles">
                        <i class="material-icons">article</i>
                        <div class="empty-text">暂无文章</div>
                        <div class="empty-hint">请先在设置中配置数据源，然后开始获取文章</div>
                    </div>
                </div>
            </div>

            <!-- 进度区域 -->
            <div class="progress-section">
                <div class="progress-info">
                    <span id="progress-text">等待开始...</span>
                    <span id="progress-percentage">0%</span>
                </div>
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill"></div>
                </div>
            </div>

            <!-- 日志区域 -->
            <div class="log-section">
                <div class="log-header">
                    <h3 class="section-title">
                        <i class="material-icons">article</i>
                        处理日志
                    </h3>
                    <button id="clear-log" class="btn-icon" title="清空日志">
                        <i class="material-icons">clear</i>
                    </button>
                </div>
                <div id="log-container" class="log-container">
                    <div class="log-content" id="log-content"></div>
                </div>
            </div>
        </main>
    </div>

    <!-- 模态对话框 -->
    <div id="modal-overlay" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <button class="btn-icon" id="modal-close">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer" id="modal-footer">
                <!-- 动态按钮 -->
            </div>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div id="history-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>历史输入记录</h3>
                <button class="btn-icon" id="history-modal-close">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <div class="modal-body">
                <div class="history-controls">
                    <div class="search-box">
                        <input type="text" id="history-search" placeholder="搜索历史记录..." class="input-field">
                        <i class="material-icons">search</i>
                    </div>
                    <button id="clear-all-history" class="btn-secondary">
                        <i class="material-icons">delete</i>
                        清空历史
                    </button>
                </div>
                <div id="history-list" class="history-list">
                    <!-- 动态生成历史记录 -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancel-history" class="btn-secondary">取消</button>
                <button id="use-history" class="btn-primary" disabled>使用选中记录</button>
            </div>
        </div>
    </div>

    <!-- 设置页面模态框 -->
    <div id="settings-modal" class="modal-overlay hidden">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>设置</h3>
                <button class="btn-icon" id="settings-modal-close">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-tabs">
                    <button class="settings-tab active" data-tab="input">数据输入</button>
                    <button class="settings-tab" data-tab="api">API设置</button>
                </div>
                
                <!-- 数据输入设置 -->
                <div class="settings-content input-settings active">
                    <h4 class="settings-section-title">
                        <i class="material-icons">input</i>
                        数据输入设置
                    </h4>
                    
                    <!-- 文本输入区域 -->
                        <div class="text-input-area">
                            <div class="input-header">
                                <label for="url-text-input">URL列表 (每行一个):</label>
                                <div class="input-actions">
                                    <button id="history-btn" class="btn-icon" title="历史记录">
                                        <i class="material-icons">history</i>
                                    </button>
                                    <button id="clear-text" class="btn-icon" title="清空">
                                        <i class="material-icons">clear</i>
                                    </button>
                                </div>
                            </div>
                            <textarea id="url-text-input" placeholder="请输入URL列表，每行一个URL..." class="text-input-field" rows="5"></textarea>
                            <div class="text-input-info">
                                <span id="url-count">URL数量: 0</span>
                                <button id="save-to-history" class="btn-text">保存到历史</button>
                            </div>
                        </div>
                </div>
                
                <!-- API设置 -->
                <div class="settings-content api-settings">
                    <h4 class="settings-section-title">
                        <i class="material-icons">api</i>
                        API配置
                    </h4>
                    <div class="api-config">
                        <div class="config-row">
                            <label for="model">模型:</label>
                            <input type="text" id="model" value="deepseek-ai/DeepSeek-V3" class="input-field">
                        </div>
                        <div class="config-row">
                            <label for="api-url">API URL:</label>
                            <input type="text" id="api-url" value="https://api.siliconflow.cn/v1/chat/completions" class="input-field">
                        </div>
                        <div class="config-row">
                            <label for="api-key">API Key:</label>
                            <input type="password" id="api-key" placeholder="请输入API密钥..." class="input-field">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancel-settings" class="btn-secondary">取消</button>
                <button id="save-settings" class="btn-primary">保存设置</button>
            </div>
        </div>
    </div>

    <script src="scripts/utils.js"></script>
    <script src="scripts/components.js"></script>
    <script src="scripts/main.js"></script>
</body>
</html> 