const axios = require('axios');
const { format } = require('date-fns');
const { zhCN } = require('date-fns/locale');

/**
 * 清理markdown数据
 */
function cleanMarkdown(markdownData) {
  // 分割并取最后部分
  const parts = markdownData.split('\n\n | \n');
  markdownData = parts[parts.length - 1];
  
  // 清理各种不需要的内容
  markdownData = markdownData
    .replace(/\*   \[.*?\]\(.*?\)/g, '')
    .replace(/    \n\n/g, '')
    .replace(/\[.*?javascript:void\(0\);.*?\n/g, '')
    .replace(/\[.*?We use cookies on our website to.*?$/g, '');
  
  // 限制长度
  return markdownData.substring(0, 15000);
}

/**
 * 自定义错误类
 */
class NewsScraperError extends Error {
  constructor(message) {
    super(message);
    this.name = 'NewsScraperError';
  }
}

/**
 * 重试装饰器
 */
async function retry(fn, retries = 3, delay = 1000) {
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === retries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
    }
  }
}

/**
 * 新闻爬虫API类
 */
class NewsScraperAPI {
  constructor() {
    this.scrapeUrl = "http://*************:3002/v1/scrape";
    this.apiUrl = "https://api.theoremhub.asia/v1/chat/completions";
    this.apiKey = "linux.do";
    this.model = "gpt-4o";
    
    // 创建axios实例
    this.httpClient = axios.create({
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/json',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      }
    });
  }

  /**
   * 更新API配置
   */
  updateConfig(config) {
    if (config.model) this.model = config.model;
    if (config.apiUrl) this.apiUrl = config.apiUrl;
    if (config.apiKey) this.apiKey = config.apiKey;
  }

  /**
   * 获取今天日期
   */
  getToday() {
    return format(new Date(), 'yyyy年MM月dd日', { locale: zhCN });
  }

  /**
   * 获取昨天日期
   */
  getYesterday() {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return format(yesterday, 'yyyy年MM月dd日', { locale: zhCN });
  }

  /**
   * 从网站获取文章列表
   */
  async getArticlesFromSite(url) {
    return retry(async () => {
      try {
        console.log(`开始获取网站文章列表: ${url}`);

        // 第一步：爬取网站内容
        const scrapeResponse = await this.httpClient.post(this.scrapeUrl, {
          url: url,
          onlyMainContent: false,
          excludeTags: ["img", "input"]
        });

        if (!scrapeResponse.data.success) {
          throw new NewsScraperError("爬取服务返回失败状态");
        }

        let markdownData = scrapeResponse.data.data?.markdown || '';
        markdownData = cleanMarkdown(markdownData);

        // 第二步：使用AI分析内容
        const prompt = `你必须遵守以下工作原则：
- 今天是${this.getToday()}，仅提取时间为${this.getYesterday()}、${this.getToday()}的内容，没有则返回{"articles": []}(不要任何解释说明)。
- 仅提取网络安全相关新闻。
- 以标准json数组形式({"articles": [{"raw_title": "原文标题","title": "中文标题", "url": "链接", "date": "日期", "origin": "来源", "tag": "标签"}]})输出。
- 若该时间段无目标内容、文档为空，则返回{"articles": []}。
- title必须翻译为简体中文，raw_title保持原文不变。
- tag为文章的分类标签，严格按照以下规则选择：
  * "涉台"：涉及台湾地区的政治、经济、军事、外交、网络安全等事务
  * "涉美"：涉及美国的政策、法规、经济、科技、外交、网络安全等事务，包括美国政府部门发布的规定、美国企业动态、中美关系等
  * "涉俄乌"：涉及俄罗斯、乌克兰冲突相关的军事、政治、经济制裁、网络攻击等事务
  * "网络安全及其他"：纯技术性网络安全话题、其他不属于上述三类的网络安全内容
- 标签判断优先级：如果新闻同时涉及多个分类，按"涉台" > "涉美" > "涉俄乌" > "网络安全及其他"的优先级选择。
- 时间判断：严格按照文档中明确标注的发布时间进行筛选，模糊时间描述不予采纳。

请从以下文档中提取符合条件的网络安全新闻：
${markdownData}`;

        const aiResponse = await this.httpClient.post(this.apiUrl, {
          model: this.model,
          messages: [
            {
              role: "system",
              content: "你是一个专业的新闻分析助手，你将读取用户提供的markdown化的网页数据，并从中提取出所有满足条件的新闻URL，将用JSON格式返回."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          temperature: 0.4,
          presence_penalty: 0.1,
          frequency_penalty: 0.1,
          top_p: 0.9,
          stop: [],
          response_format: {
            type: "json_object"
          }
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          timeout: 70000
        });

        if (!aiResponse.data.choices || aiResponse.data.choices.length === 0) {
          return [];
        }

        let content = aiResponse.data.choices[0].message.content;

        // 清理内容
        if (content.includes("[CONTEXT_ID:")) {
          content = content.substring(0, content.indexOf("[CONTEXT_ID:"));
        }

        content = content.replace(/```json/g, "").replace(/```/g, "");
        console.log('AI响应:', content);

        // 提取JSON数据
        const jsonMatch = content.match(/\{.*"articles".*\}/s);
        if (!jsonMatch) {
          console.warn('未找到有效的JSON数据');
          return [];
        }

        const articlesData = JSON.parse(jsonMatch[0]);
        const articles = articlesData.articles || [];

        console.log(`成功获取 ${articles.length} 篇文章`);

        return articles.map(article => ({
          title: article.title || '',
          raw_title: article.raw_title || '',
          url: article.url || '',
          date: article.date || '',
          origin: article.origin || '',
          tag: article.tag || '网络安全及其他'
        }));

      } catch (error) {
        console.error(`获取文章列表失败: ${error.message}`);
        throw error;
      }
    }, 3, 30000);
  }

  /**
   * 处理单篇文章
   */
  async processArticle(article) {
    return retry(async () => {
      try {
        console.log(`开始处理文章: ${article.url}`);

        // 获取文章内容
        const scrapeResponse = await this.httpClient.post(this.scrapeUrl, {
          url: article.url,
          onlyMainContent: false,
          excludeTags: ["img", "a", "input"]
        });

        if (!scrapeResponse.data.success) {
          throw new NewsScraperError("获取文章失败");
        }

        let content = scrapeResponse.data.data?.markdown || '';
        content = cleanMarkdown(content);

        // 处理文章内容
        const prompt = `你必须遵守以下工作原则：
- 以json格式输出，无需缩进、换行：{"is_article": true, "title": 标题, "content": 摘要, "date": 发布时间, "origin": 文章来源主体名, "tag": 标签}。
- 若无法提取文章则返回{"is_article": false}
- 标题应简明扼要说明事件，突出事件影响和相关数据。
- title、content必须为简体中文，特别强调繁体中文要修改为简体中文，标题中间不要有空格。
- date格式示例：2025年1月1日
- tag为文章的分类标签，严格按照以下规则选择：
  * "涉台"：涉及台湾地区的政治、经济、军事、外交、网络安全等事务
  * "涉美"：涉及美国的政策、法规、经济、科技、外交、网络安全等事务，包括美国政府部门发布的规定、美国企业动态、中美关系等
  * "涉俄乌"：涉及俄罗斯、乌克兰冲突相关的军事、政治、经济制裁、网络攻击等事务
  * "网络安全及其他"：纯技术性网络安全话题、其他不属于上述三类的内容
- 标签判断优先级：如果新闻同时涉及多个分类，按"涉台" > "涉美" > "涉俄乌" > "网络安全及其他"的优先级选择
- content应控制在200字左右，请直接总结新闻的核心内容和关键信息，使用客观陈述的语气，避免使用'文章介绍了'、'报道称'、'据悉'等导览性表述，直接呈现事实要点。

请处理以下新闻：
${content}`;

        const aiResponse = await this.httpClient.post(this.apiUrl, {
          model: this.model,
          messages: [
            {
              role: "system",
              content: "你是一位中国立场的威胁情报专家，负责将新闻事件总结提炼并翻译为中文，使用JSON格式返回."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          temperature: 0.6,
          presence_penalty: 0.1,
          frequency_penalty: 0.1,
          top_p: 0.9,
          stop: [],
          response_format: {
            type: "json_object"
          }
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          timeout: 60000
        });

        if (!aiResponse.data.choices || aiResponse.data.choices.length === 0) {
          return null;
        }

        let content_response = aiResponse.data.choices[0].message.content;

        // 清理内容
        if (content_response.includes("[CONTEXT_ID:")) {
          content_response = content_response.substring(0, content_response.indexOf("[CONTEXT_ID:"));
        }

        content_response = content_response.replace(/```json/g, "").replace(/```/g, "");

        // 提取JSON数据
        const jsonMatch = content_response.match(/\{.*"is_article".*\}/s);
        if (!jsonMatch) {
          console.warn('未找到有效的JSON数据');
          return null;
        }

        const articleData = JSON.parse(jsonMatch[0]);

        if (!articleData.is_article) {
          console.log(`文章处理失败: ${article.url} - 不是有效的文章`);
          return null;
        }

        const processedArticle = {
          title: articleData.title,
          url: article.url,
          content: articleData.content,
          date: articleData.date,
          origin: articleData.origin,
          tag: articleData.tag || '网络安全及其他'
        };

        console.log(`文章处理成功: ${processedArticle.title}`);
        
        // 添加延迟避免频率限制
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return processedArticle;

      } catch (error) {
        console.error(`处理文章失败: ${article.url} - ${error.message}`);
        throw error;
      }
    }, 1, 3000);
  }
}

module.exports = NewsScraperAPI; 