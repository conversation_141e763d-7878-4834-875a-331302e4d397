// 工具函数模块

/**
 * 日期格式化工具
 */
const DateUtils = {
  /**
   * 格式化日期为中文格式
   */
  formatToChinese(dateStr) {
    try {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    } catch (error) {
      return dateStr;
    }
  },

  /**
   * 获取当前时间戳
   */
  getCurrentTimestamp() {
    return new Date().toLocaleString('zh-CN');
  }
};

/**
 * DOM操作工具
 */
const DOMUtils = {
  /**
   * 创建元素
   */
  createElement(tag, className = '', textContent = '') {
    const element = document.createElement(tag);
    if (className) element.className = className;
    if (textContent) element.textContent = textContent;
    return element;
  },

  /**
   * 切换元素可见性
   */
  toggleVisible(element, visible) {
    if (visible) {
      element.classList.remove('hidden');
    } else {
      element.classList.add('hidden');
    }
  },

  /**
   * 安全地获取元素
   */
  safeGetElement(id) {
    const element = document.getElementById(id);
    if (!element) {
      console.warn(`元素未找到: ${id}`);
    }
    return element;
  },

  /**
   * 滚动到元素底部
   */
  scrollToBottom(element) {
    element.scrollTop = element.scrollHeight;
  }
};

/**
 * 文本处理工具
 */
const TextUtils = {
  /**
   * 转义HTML
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  },

  /**
   * 截断文本
   */
  truncate(text, maxLength = 100) {
    if (text.length <= maxLength) return text;
    return text.substr(0, maxLength) + '...';
  },

  /**
   * 数字转中文
   */
  numberToChinese(num) {
    const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    if (num <= 10) {
      return chineseNums[num];
    } else if (num < 20) {
      return `十${num > 10 ? chineseNums[num - 10] : ''}`;
    } else {
      return `${chineseNums[Math.floor(num / 10)]}十${num % 10 ? chineseNums[num % 10] : ''}`;
    }
  }
};

/**
 * 事件工具
 */
const EventUtils = {
  /**
   * 防抖函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * 节流函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
};

/**
 * 存储工具
 */
const StorageUtils = {
  /**
   * 保存到localStorage
   */
  save(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('保存到localStorage失败:', error);
    }
  },

  /**
   * 从localStorage读取
   */
  load(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('从localStorage读取失败:', error);
      return defaultValue;
    }
  },

  /**
   * 移除localStorage项
   */
  remove(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('从localStorage移除失败:', error);
    }
  }
};

/**
 * 验证工具
 */
const ValidationUtils = {
  /**
   * 验证URL格式
   */
  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  },

  /**
   * 验证文件路径
   */
  isValidFilePath(path) {
    return path && path.trim().length > 0;
  },

  /**
   * 验证API配置
   */
  isValidApiConfig(config) {
    return config.model && config.apiUrl && config.apiKey;
  }
};

/**
 * 数组工具
 */
const ArrayUtils = {
  /**
   * 安全地获取数组项
   */
  safeGet(array, index, defaultValue = null) {
    return array && array[index] !== undefined ? array[index] : defaultValue;
  },

  /**
   * 数组去重
   */
  unique(array, key = null) {
    if (!key) {
      return [...new Set(array)];
    }
    const seen = new Set();
    return array.filter(item => {
      const value = item[key];
      if (seen.has(value)) {
        return false;
      }
      seen.add(value);
      return true;
    });
  },

  /**
   * 数组分组
   */
  groupBy(array, key) {
    return array.reduce((groups, item) => {
      const group = item[key];
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {});
  }
};

/**
 * URL处理工具
 */
const UrlUtils = {
  /**
   * 从文本中解析URL列表
   */
  parseUrlsFromText(text) {
    if (!text || typeof text !== 'string') return [];
    
    const lines = text.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
    
    const urls = [];
    for (const line of lines) {
      if (ValidationUtils.isValidUrl(line)) {
        urls.push(line);
      }
    }
    
    return ArrayUtils.unique(urls);
  },

  /**
   * 清理和格式化URL文本
   */
  cleanUrlText(text) {
    if (!text) return '';
    
    return text.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n');
  },

  /**
   * 计算URL数量
   */
  countUrls(text) {
    return this.parseUrlsFromText(text).length;
  }
};

/**
 * 历史记录工具
 */
const HistoryUtils = {
  STORAGE_KEY: 'urlInputHistory',
  MAX_HISTORY_ITEMS: 20,

  /**
   * 保存输入历史
   */
  saveHistory(urlText) {
    if (!urlText || urlText.trim().length === 0) return;
    
    const cleanText = UrlUtils.cleanUrlText(urlText);
    const urls = UrlUtils.parseUrlsFromText(cleanText);
    
    if (urls.length === 0) return;
    
    const historyItem = {
      id: Date.now(),
      text: cleanText,
      urls: urls,
      urlCount: urls.length,
      timestamp: new Date().toISOString(),
      preview: this.generatePreview(cleanText)
    };
    
    const history = this.getHistory();
    
    // 移除重复项（基于文本内容）
    const filteredHistory = history.filter(item => item.text !== cleanText);
    
    // 添加新项到开头
    filteredHistory.unshift(historyItem);
    
    // 限制历史记录数量
    const limitedHistory = filteredHistory.slice(0, this.MAX_HISTORY_ITEMS);
    
    StorageUtils.save(this.STORAGE_KEY, limitedHistory);
  },

  /**
   * 获取历史记录
   */
  getHistory() {
    return StorageUtils.load(this.STORAGE_KEY, []);
  },

  /**
   * 删除特定历史记录
   */
  deleteHistory(id) {
    const history = this.getHistory();
    const filteredHistory = history.filter(item => item.id !== id);
    StorageUtils.save(this.STORAGE_KEY, filteredHistory);
  },

  /**
   * 清空所有历史记录
   */
  clearHistory() {
    StorageUtils.remove(this.STORAGE_KEY);
  },

  /**
   * 生成预览文本
   */
  generatePreview(text, maxLength = 100) {
    if (!text) return '';
    
    const lines = text.split('\n').filter(line => line.trim().length > 0);
    const preview = lines.slice(0, 3).join('\n');
    
    if (preview.length > maxLength) {
      return preview.substring(0, maxLength) + '...';
    }
    
    if (lines.length > 3) {
      return preview + '\n...';
    }
    
    return preview;
  },

  /**
   * 搜索历史记录
   */
  searchHistory(searchTerm) {
    if (!searchTerm || searchTerm.trim().length === 0) {
      return this.getHistory();
    }
    
    const term = searchTerm.toLowerCase();
    const history = this.getHistory();
    
    return history.filter(item => 
      item.text.toLowerCase().includes(term) ||
      item.preview.toLowerCase().includes(term)
    );
  }
};

// 导出所有工具
window.Utils = {
  Date: DateUtils,
  DOM: DOMUtils,
  Text: TextUtils,
  Event: EventUtils,
  Storage: StorageUtils,
  Validation: ValidationUtils,
  Array: ArrayUtils,
  Url: UrlUtils,
  History: HistoryUtils
}; 