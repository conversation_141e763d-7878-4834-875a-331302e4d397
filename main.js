const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const NewsScraperAPI = require('./src/api/scraper');

// 定义CSS变量
const themes = {
  primary: '#1976d2',
  secondary: '#424242',
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  background: '#f5f5f5',
  surface: '#ffffff',
  text: '#212121',
  textSecondary: '#757575'
};

let mainWindow;
let scraperAPI;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    title: '新闻爬取工具',
    show: false
  });

  mainWindow.loadFile('src/renderer/index.html');

  // 开发模式下打开开发者工具
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // 窗口准备就绪后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 初始化爬虫API
  scraperAPI = new NewsScraperAPI();
}

app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC 处理程序
ipcMain.handle('update-api-config', async (event, config) => {
  try {
    scraperAPI.updateConfig(config);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-articles-from-site', async (event, url) => {
  try {
    const articles = await scraperAPI.getArticlesFromSite(url);
    return { success: true, articles };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('process-article', async (event, article) => {
  try {
    const processedArticle = await scraperAPI.processArticle(article);
    
    if (processedArticle) {
      return { success: true, article: processedArticle };
    } else {
      return { success: false, error: '文章处理失败：不是有效的文章或无法提取内容' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('generate-report', async (event, articles) => {
  try {
    const reportContent = generateReportContent(articles);
    const outputDir = path.join(require('os').homedir(), 'Desktop');
    const today = scraperAPI.getToday();
    const outputPath = path.join(outputDir, `${today}.txt`);
    
    await fs.writeFile(outputPath, reportContent, 'utf-8');
    
    // 同时保存到生成结果目录
    const resultDir = path.join(__dirname, '..', '生成结果');
    try {
      await fs.mkdir(resultDir, { recursive: true });
      await fs.writeFile(path.join(resultDir, `${today}.txt`), reportContent, 'utf-8');
    } catch (err) {
      console.warn('保存到生成结果目录失败:', err.message);
    }
    
    return { success: true, outputPath };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('save-results', async (event, results) => {
  try {
    const { processedArticles, failedArticles, summary } = results;
    
    // 生成保存内容
    const timestamp = new Date().toLocaleString('zh-CN').replace(/[:/\s]/g, '-');
    const fileName = `处理结果-${timestamp}.json`;
    
    // 创建保存目录
    const saveDir = path.join(require('os').homedir(), 'Desktop', '新闻处理结果');
    await fs.mkdir(saveDir, { recursive: true });
    
    const filePath = path.join(saveDir, fileName);
    
    // 格式化保存数据
    const saveData = {
      summary: {
        ...summary,
        savedAt: new Date().toISOString(),
        version: '1.0'
      },
      processedArticles: processedArticles.map(article => ({
        title: article.title,
        origin: article.origin,
        date: article.date,
        url: article.url,
        content: article.content,
        tag: article.tag,
        processedData: article.processedData,
        processedAt: article.processedAt
      })),
      failedArticles: failedArticles.map(article => ({
        title: article.title,
        origin: article.origin,
        date: article.date,
        url: article.url,
        error: article.error,
        failedAt: article.failedAt || article.retryFailedAt
      }))
    };
    
    // 保存JSON文件
    await fs.writeFile(filePath, JSON.stringify(saveData, null, 2), 'utf-8');
    
    // 同时生成可读的文本报告
    const reportContent = generateProcessResultReport(saveData);
    const reportFileName = `处理报告-${timestamp}.txt`;
    const reportPath = path.join(saveDir, reportFileName);
    await fs.writeFile(reportPath, reportContent, 'utf-8');
    
    return { 
      success: true, 
      filePath: filePath,
      reportPath: reportPath
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

function generateReportContent(articles) {
  const numberToChinese = (num) => {
    const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    if (num <= 10) {
      return chineseNums[num];
    } else if (num < 20) {
      return `十${num > 10 ? chineseNums[num - 10] : ''}`;
    } else {
      return `${chineseNums[Math.floor(num / 10)]}十${num % 10 ? chineseNums[num % 10] : ''}`;
    }
  };

  let reportContent = "";
  articles.forEach((article, index) => {
    const num = index + 1;
    reportContent += (
      `（${numberToChinese(num)}）${article.title}` +
      `（来自：《${article.origin}》，发布时间：${article.date}）\n` +
      `${article.content}\n\n`
    );
  });

  return reportContent;
}

function generateProcessResultReport(saveData) {
  const { summary, processedArticles, failedArticles } = saveData;
  
  let report = "=".repeat(60) + "\n";
  report += "                    新闻处理结果报告\n";
  report += "=".repeat(60) + "\n\n";
  
  // 处理摘要
  report += "【处理摘要】\n";
  report += `处理时间：${new Date(summary.processedAt).toLocaleString('zh-CN')}\n`;
  report += `保存时间：${new Date(summary.savedAt).toLocaleString('zh-CN')}\n`;
  report += `总选择文章：${summary.totalSelected} 篇\n`;
  report += `成功处理：${summary.successCount} 篇\n`;
  report += `处理失败：${summary.failCount} 篇\n`;
  report += `成功率：${((summary.successCount / summary.totalSelected) * 100).toFixed(1)}%\n\n`;
  
  // 成功处理的文章
  if (processedArticles.length > 0) {
    report += "=".repeat(60) + "\n";
    report += "【成功处理的文章】\n";
    report += "=".repeat(60) + "\n\n";
    
    processedArticles.forEach((article, index) => {
      report += `${index + 1}. ${article.title}\n`;
      report += `   来源：${article.origin}\n`;
      report += `   日期：${article.date}\n`;
      report += `   标签：${article.tag || '未分类'}\n`;
      report += `   链接：${article.url || '未提供'}\n`;
      report += `   处理时间：${new Date(article.processedAt).toLocaleString('zh-CN')}\n`;
      if (article.processedData) {
        report += `   处理结果：${typeof article.processedData === 'string' ? article.processedData : JSON.stringify(article.processedData)}\n`;
      }
      report += `   内容：${article.content ? article.content : '无内容'}\n\n`;
    });
  }
  
  // 失败的文章
  if (failedArticles.length > 0) {
    report += "=".repeat(60) + "\n";
    report += "【处理失败的文章】\n";
    report += "=".repeat(60) + "\n\n";
    
    failedArticles.forEach((article, index) => {
      report += `${index + 1}. ${article.title}\n`;
      report += `   来源：${article.origin}\n`;
      report += `   日期：${article.date}\n`;
      report += `   链接：${article.url || '未提供'}\n`;
      report += `   失败时间：${new Date(article.failedAt).toLocaleString('zh-CN')}\n`;
      report += `   失败原因：${article.error}\n\n`;
    });
  }
  
  report += "=".repeat(60) + "\n";
  report += "                      报告结束\n";
  report += "=".repeat(60) + "\n";
  
  return report;
}

// 获取主题配置
ipcMain.handle('get-theme', async () => {
  return themes;
});

// 深度研究功能 - 打开子窗口
ipcMain.handle('open-deep-research-window', async (_event, articleTitle) => {
  try {
    // 创建子窗口
    const deepResearchWindow = new BrowserWindow({
      width: 1000,
      height: 700,
      parent: mainWindow,
      modal: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      },
      title: '深度研究 - ' + articleTitle,
      show: false
    });

    // 加载目标网页
    await deepResearchWindow.loadURL('https://www.deepseek.com/');

    // 窗口准备就绪后显示
    deepResearchWindow.once('ready-to-show', () => {
      deepResearchWindow.show();

      // 等待页面加载完成后执行自动化操作
      setTimeout(async () => {
        try {
          await performDeepResearchAutomation(deepResearchWindow, articleTitle);
        } catch (error) {
          console.error('自动化操作失败:', error);
        }
      }, 3000);
    });

    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// 执行深度研究自动化操作
async function performDeepResearchAutomation(window, articleTitle) {
  try {
    // 构建研究提示文本
    const researchPrompt = `请深入研究分析主题：${articleTitle}，站在中国立场，按照公文格式编写威胁情报`;

    // 执行自动化脚本
    const automationScript = `
      (function() {
        // 等待页面完全加载
        function waitForElement(selector, timeout = 15000) {
          return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function check() {
              const element = document.querySelector(selector);
              if (element) {
                resolve(element);
              } else if (Date.now() - startTime > timeout) {
                reject(new Error('元素未找到: ' + selector));
              } else {
                setTimeout(check, 200);
              }
            }

            check();
          });
        }

        // 查找包含特定SVG的按钮
        function findButtonWithSvg(svgSelector) {
          const buttons = document.querySelectorAll('button');
          for (let button of buttons) {
            if (button.querySelector(svgSelector)) {
              return button;
            }
          }
          return null;
        }

        async function performAutomation() {
          try {
            console.log('开始执行深度研究自动化...');

            // 等待页面加载完成
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 1. 查找并点击Deep Thinking模式按钮
            console.log('正在查找Deep Thinking按钮...');
            let deepThinkingBtn = findButtonWithSvg('svg.lucide-lightbulb');
            if (!deepThinkingBtn) {
              // 备用选择器
              deepThinkingBtn = findButtonWithSvg('svg[class*="lightbulb"]');
            }

            if (deepThinkingBtn && !deepThinkingBtn.classList.contains('active')) {
              deepThinkingBtn.click();
              console.log('已点击Deep Thinking模式');
              await new Promise(resolve => setTimeout(resolve, 1000));
            } else {
              console.log('Deep Thinking按钮已激活或未找到');
            }

            // 2. 查找并点击Investigation模式按钮
            console.log('正在查找Investigation按钮...');
            let investigationBtn = findButtonWithSvg('svg[viewBox="0 0 512 512"]');
            if (!investigationBtn) {
              // 备用选择器 - 查找包含Investigation文本的按钮
              const buttons = document.querySelectorAll('button');
              for (let button of buttons) {
                if (button.textContent.includes('Investigation')) {
                  investigationBtn = button;
                  break;
                }
              }
            }

            if (investigationBtn && !investigationBtn.classList.contains('active')) {
              investigationBtn.click();
              console.log('已点击Investigation模式');
              await new Promise(resolve => setTimeout(resolve, 1000));
            } else {
              console.log('Investigation按钮已激活或未找到');
            }

            // 3. 查找输入框并输入内容
            console.log('正在查找输入框...');
            let inputBox = await waitForElement('.tiptap.ProseMirror[contenteditable="true"]');
            if (!inputBox) {
              // 备用选择器
              inputBox = document.querySelector('[contenteditable="true"]');
            }

            if (inputBox) {
              // 清空现有内容
              inputBox.innerHTML = '';
              inputBox.focus();

              // 输入新内容
              const paragraph = document.createElement('p');
              paragraph.textContent = '${researchPrompt}';
              inputBox.appendChild(paragraph);

              // 触发多种事件确保内容被识别
              inputBox.dispatchEvent(new Event('input', { bubbles: true }));
              inputBox.dispatchEvent(new Event('change', { bubbles: true }));
              inputBox.dispatchEvent(new KeyboardEvent('keyup', { bubbles: true }));

              console.log('已输入研究提示');
              await new Promise(resolve => setTimeout(resolve, 1000));

              // 4. 查找并点击提交按钮
              console.log('正在查找提交按钮...');
              let submitBtn = findButtonWithSvg('svg.lucide-arrow-up');
              if (!submitBtn) {
                // 备用选择器
                submitBtn = findButtonWithSvg('svg[class*="arrow-up"]');
              }

              if (submitBtn) {
                submitBtn.click();
                console.log('已点击提交按钮');
                return { success: true, message: '深度研究已启动' };
              } else {
                throw new Error('未找到提交按钮');
              }
            } else {
              throw new Error('未找到输入框');
            }
          } catch (error) {
            console.error('自动化操作失败:', error);
            return { success: false, error: error.message };
          }
        }

        return performAutomation();
      })();
    `;

    // 执行脚本
    const result = await window.webContents.executeJavaScript(automationScript);
    console.log('深度研究自动化结果:', result);

  } catch (error) {
    console.error('执行深度研究自动化失败:', error);
  }
}