# 新闻爬取工具 - Electron版本

一个现代化的新闻信息爬取工具，采用Electron技术构建，提供美观的用户界面和强大的功能。

## 功能特性

### 🚀 核心功能
- **批量网站爬取**：支持从多个新闻网站批量获取文章列表
- **智能文章筛选**：使用AI自动筛选网络安全相关新闻
- **文章内容处理**：自动翻译、总结和格式化文章内容
- **报告生成**：生成格式化的中文新闻报告

### 🎨 界面特性
- **现代化UI**：采用Material Design设计语言
- **深色主题**：支持浅色/深色主题切换
- **响应式设计**：适配不同屏幕尺寸
- **CSS变量**：使用CSS变量定义颜色主题

### 🔧 技术特性
- **模块化架构**：组件化设计，代码解耦
- **错误处理**：完善的错误处理和重试机制
- **进度跟踪**：实时显示处理进度
- **日志系统**：详细的操作日志记录

## 系统要求

- **操作系统**：Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**：16.0 或更高版本
- **内存**：建议 4GB 以上
- **存储**：至少 200MB 可用空间

## 安装与运行

### 方法一：使用启动脚本（推荐）

1. 双击运行 `启动.bat`（Windows）
2. 脚本会自动检查依赖并启动应用

### 方法二：手动安装

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动应用**
   ```bash
   npm start
   ```

3. **开发模式**（带开发者工具）
   ```bash
   npm run dev
   ```

4. **打包应用**
   ```bash
   npm run build
   ```

## 使用指南

### 1. 配置设置

#### 文件选择
- 点击"浏览"按钮选择包含URL列表的文本文件
- 文件格式：每行一个URL

#### API设置
- **模型**：AI模型名称（默认：Qwen/Qwen2.5-72B-Instruct-128K）
- **API URL**：AI服务API地址
- **API Key**：AI服务密钥

### 2. 处理流程

1. **文件读取**：读取URL文件中的网站列表
2. **文章获取**：从各网站爬取文章列表
3. **文章筛选**：使用AI筛选网络安全相关文章
4. **文章选择**：用户手动选择要处理的文章
5. **内容处理**：AI处理文章内容（翻译、总结）
6. **报告生成**：生成最终的中文报告

### 3. 重试机制

- **URL重试**：对失败的网站URL支持重试
- **文章重试**：对处理失败的文章支持重试
- **全量重试**：支持重新处理所有内容

## 项目结构

```
new_src/
├── main.js                 # Electron主进程
├── preload.js             # 预加载脚本
├── package.json           # 项目配置
├── src/
│   ├── api/
│   │   └── scraper.js     # 爬虫API模块
│   └── renderer/          # 渲染进程
│       ├── index.html     # 主界面
│       ├── styles/        # 样式文件
│       │   ├── themes.css      # 主题变量
│       │   ├── main.css        # 主样式
│       │   └── components.css  # 组件样式
│       └── scripts/       # JavaScript文件
│           ├── utils.js        # 工具函数
│           ├── components.js   # 组件管理
│           └── main.js         # 主应用逻辑
├── 启动.bat               # Windows启动脚本
└── README.md              # 项目说明
```

## 技术架构

### 前端技术栈
- **Electron**：跨平台桌面应用框架
- **HTML5/CSS3**：现代Web标准
- **JavaScript ES6+**：现代JavaScript语法
- **Material Icons**：Google图标库

### 后端技术栈
- **Node.js**：JavaScript运行环境
- **Axios**：HTTP客户端
- **date-fns**：日期处理库

### 设计模式
- **MVC架构**：模型-视图-控制器分离
- **模块化设计**：功能模块化，便于维护
- **组件化开发**：UI组件复用
- **事件驱动**：基于事件的交互模式

## CSS变量系统

项目使用CSS变量定义颜色主题，支持主题切换：

```css
:root {
  --color-primary: #1976d2;
  --color-secondary: #424242;
  --color-success: #4caf50;
  --color-warning: #ff9800;
  --color-error: #f44336;
  /* ... 更多变量 */
}
```

## 开发指南

### 添加新功能

1. **创建组件**：在 `scripts/components.js` 中添加新组件
2. **添加样式**：在 `styles/components.css` 中定义样式
3. **集成功能**：在 `scripts/main.js` 中集成新功能

### 自定义主题

1. 修改 `styles/themes.css` 中的CSS变量
2. 添加新的主题选择器 `[data-theme="your-theme"]`
3. 在 `scripts/components.js` 的 `ThemeManager` 中添加主题选项

### 调试技巧

- 使用 `npm run dev` 启动开发模式
- 打开开发者工具查看控制台输出
- 使用 `window.app` 访问应用实例
- 查看日志面板了解处理流程

## 故障排除

### 常见问题

1. **依赖安装失败**
   - 检查网络连接
   - 尝试使用国内镜像：`npm config set registry https://registry.npmmirror.com`

2. **应用启动失败**
   - 确认Node.js版本 >= 16.0
   - 删除 `node_modules` 文件夹后重新安装

3. **爬取失败**
   - 检查网络连接
   - 确认爬虫服务地址可访问
   - 查看日志了解具体错误

4. **API调用失败**
   - 确认API Key正确
   - 检查API服务状态
   - 查看网络代理设置

### 日志文件

应用运行日志保存在界面的日志面板中，包含：
- 操作记录
- 错误信息
- 处理进度
- API调用结果

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的新闻爬取功能
- 现代化UI界面
- 主题切换支持

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件到开发者邮箱

---

**注意**：本工具仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。 