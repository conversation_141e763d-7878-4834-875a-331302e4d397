/* 主样式文件 */

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: var(--font-family);
  font-size: var(--font-size);
  line-height: var(--line-height);
  color: var(--color-text);
  background-color: var(--color-background);
}

/* 应用主容器 */
#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 标题栏 */
.app-header {
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  padding: var(--spacing);
  box-shadow: var(--shadow-1);
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--spacing);
  gap: var(--spacing);
  overflow: hidden;
  height: calc(100vh - 80px); /* 减去标题栏高度 */
}

/* 配置面板 */
.config-panel {
  background-color: var(--color-surface);
  border-radius: var(--border-radius);
  padding: var(--spacing);
  box-shadow: var(--shadow-1);
  border: 1px solid var(--color-border);
  flex-shrink: 0; /* 防止被压缩 */
}

.config-section {
  margin-bottom: var(--spacing-lg);
}

.config-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--spacing);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.section-title .material-icons {
  font-size: 20px;
  color: var(--color-primary);
}

/* 文件选择器 */
.file-selector {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.file-selector .input-field {
  flex: 1;
}

/* API配置 */
.api-config {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing);
}

.config-row {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.config-row label {
  font-weight: 500;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* 控制区域 */
.control-section {
  display: flex;
  justify-content: flex-end;
  padding-top: var(--spacing);
  border-top: 1px solid var(--color-border-light);
}

/* 进度区域 */
.progress-section {
  background-color: var(--color-surface);
  border-radius: var(--border-radius);
  padding: var(--spacing);
  box-shadow: var(--shadow-1);
  border: 1px solid var(--color-border);
  flex-shrink: 0; /* 防止被压缩 */
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

#progress-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

#progress-percentage {
  color: var(--color-primary);
  font-weight: 600;
  font-size: var(--font-size-sm);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--color-border-light);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  width: 0%;
  transition: width var(--transition);
  border-radius: var(--border-radius-sm);
}

/* 日志区域 */
.log-section {
  flex: 1;
  background-color: var(--color-surface);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-1);
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex项目缩小 */
  overflow: hidden; /* 确保不会产生意外滚动 */
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing);
  border-bottom: 1px solid var(--color-border-light);
}

.log-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.log-content {
  flex: 1;
  padding: var(--spacing);
  overflow-y: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-loose);
  white-space: pre-wrap;
  color: var(--color-text-secondary);
  background-color: var(--color-surface-variant);
  margin: 0; /* 移除可能的外边距 */
}

.log-entry {
  margin-bottom: var(--spacing-xs);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--color-border-light);
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry.error {
  color: var(--color-error);
}

.log-entry.success {
  color: var(--color-success);
}

.log-entry.warning {
  color: var(--color-warning);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
    height: calc(100vh - 70px); /* 减小标题栏高度 */
  }
  
  .config-panel,
  .progress-section,
  .log-section {
    padding: var(--spacing-sm);
  }
  
  .api-config {
    grid-template-columns: 1fr;
  }
  
  .file-selector {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-content {
    padding: 0 var(--spacing-sm);
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }
  
  .app-title {
    font-size: var(--font-size-lg);
  }

  .control-section {
    padding: var(--spacing);
  }

  .control-section .btn-primary {
    min-width: auto;
    width: 100%;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface-variant);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-dark);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-secondary);
}

/* 选择文本样式 */
::selection {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
} 