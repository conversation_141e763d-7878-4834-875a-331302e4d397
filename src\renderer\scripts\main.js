// 主应用逻辑

/**
 * 应用状态管理
 */
class AppState {
  constructor() {
    this.isProcessing = false;
    this.currentStep = '';
    this.urlText = '';
    this.urls = [];
    this.allArticles = [];
    this.selectedArticles = [];
    this.processedArticles = [];
    this.failedArticles = [];
    this.apiConfig = {
      model: 'Qwen/Qwen2.5-72B-Instruct-128K',
      apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
      apiKey: 'sk-jpvhbzmvpoyuwfavmksfykvfvlkcqgejwwjziehahyxfeuin'
    };
  }

  updateApiConfig() {
    this.apiConfig.model = document.getElementById('model').value;
    this.apiConfig.apiUrl = document.getElementById('api-url').value;
    this.apiConfig.apiKey = document.getElementById('api-key').value;
  }

  setProcessing(processing) {
    this.isProcessing = processing;
    this.updateUI();
  }

  updateUI() {
    const startBtn = document.getElementById('start-processing');
    const inputs = document.querySelectorAll('.input-field, .text-input-field');
    
    const hasValidInput = this.urlText && this.urlText.trim().length > 0;
    
    if (startBtn) {
      // 获取当前选中的文章数量
      const selectedCount = document.querySelectorAll('.article-checkbox:checked').length;
      const hasSelectedArticles = selectedCount > 0;
      
      // 根据状态更新按钮
      if (this.isProcessing) {
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="material-icons">hourglass_empty</i> 处理中...';
      } else if (hasSelectedArticles) {
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="material-icons">play_arrow</i> 开始处理';
      } else {
        startBtn.disabled = !hasValidInput;
        startBtn.innerHTML = '<i class="material-icons">download</i> 获取文章';
      }
    }

    inputs.forEach(input => {
      input.disabled = this.isProcessing;
    });

    // 更新状态指示器
    if (window.app && window.app.updateStatusIndicators) {
      window.app.updateStatusIndicators();
    }
  }
}

/**
 * 主应用类
 */
class NewsScraperApp {
  constructor() {
    this.state = new AppState();
    this.retryData = {};
    this.init();
  }

  async init() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
    } else {
      this.setupEventListeners();
    }

    // 恢复保存的配置
    this.restoreConfig();
  }

  setupEventListeners() {
    // 开始处理
    const startBtn = document.getElementById('start-processing');
    if (startBtn) {
      startBtn.addEventListener('click', () => this.startProcessing());
    }

    // API配置变化
    const configInputs = ['model', 'api-url', 'api-key'];
    configInputs.forEach(id => {
      const input = document.getElementById(id);
      if (input) {
        input.addEventListener('change', () => {
          this.state.updateApiConfig();
          this.saveConfig();
        });
      }
    });

    // 文本输入变化
    const urlTextInput = document.getElementById('url-text-input');
    if (urlTextInput) {
      urlTextInput.addEventListener('input', () => {
        this.state.urlText = urlTextInput.value;
        this.updateUrlCount();
        this.state.updateUI();
        this.saveConfig(); // 自动保存
      });
    }

    // 清空文本
    const clearTextBtn = document.getElementById('clear-text');
    if (clearTextBtn) {
      clearTextBtn.addEventListener('click', () => {
        this.clearUrlText();
      });
    }

    // 保存到历史
    const saveToHistoryBtn = document.getElementById('save-to-history');
    if (saveToHistoryBtn) {
      saveToHistoryBtn.addEventListener('click', () => {
        this.saveCurrentInputToHistory();
      });
    }

    // 历史记录按钮
    const historyBtn = document.getElementById('history-btn');
    if (historyBtn) {
      historyBtn.addEventListener('click', () => {
        this.showHistoryModal();
      });
    }

    // 设置按钮
    const settingsBtn = document.getElementById('settings-btn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.showSettingsModal();
      });
    }
  }



  updateUrlCount() {
    const count = Utils.Url.countUrls(this.state.urlText);
    const countElement = document.getElementById('url-count');
    if (countElement) {
      countElement.textContent = `URL数量: ${count}`;
    }
    this.updateStatusIndicators();
  }

  clearUrlText() {
    const urlTextInput = document.getElementById('url-text-input');
    if (urlTextInput) {
      urlTextInput.value = '';
      this.state.urlText = '';
      this.updateUrlCount();
      this.state.updateUI();
    }
  }

  saveCurrentInputToHistory() {
    if (!this.state.urlText || this.state.urlText.trim().length === 0) {
      Components.Modal.show('general', {
        title: '提示',
        body: '请先输入URL内容',
        buttons: [{ text: '确定', className: 'btn-primary', onClick: () => Components.Modal.hide('general') }]
      });
      return;
    }

    const urlCount = Utils.Url.countUrls(this.state.urlText);
    if (urlCount === 0) {
      Components.Modal.show('general', {
        title: '提示',
        body: '没有找到有效的URL',
        buttons: [{ text: '确定', className: 'btn-primary', onClick: () => Components.Modal.hide('general') }]
      });
      return;
    }

    Utils.History.saveHistory(this.state.urlText);
    Components.Log.success(`已保存 ${urlCount} 个URL到历史记录`);
  }

  showHistoryModal() {
    Components.Modal.show('history', {
      onConfirm: (selectedText) => {
        const urlTextInput = document.getElementById('url-text-input');
        if (urlTextInput) {
          urlTextInput.value = selectedText;
          this.state.urlText = selectedText;
          this.updateUrlCount();
          this.state.updateUI();
          this.updateStatusIndicators();
        }
        Components.Log.success('已从历史记录载入URL列表');
      }
    });
  }

  showSettingsModal() {
    Components.Modal.show('settings');
  }

  saveSettingsFromModal() {
    // 保存API配置
    this.state.updateApiConfig();
    
    // 保存输入模式和文本内容
    const urlTextInput = document.getElementById('url-text-input');
    if (urlTextInput) {
      this.state.urlText = urlTextInput.value;
      this.updateUrlCount();
    }
    
    // 保存所有配置
    this.saveConfig();
    
    // 更新UI状态
    this.state.updateUI();
    this.updateStatusIndicators();
    
    Components.Log.success('设置已保存');
  }

  updateStatusIndicators() {
    this.updateApiStatus();
    this.updateUrlStatus();
  }

  updateApiStatus() {
    const apiStatusElement = document.getElementById('api-status');
    if (!apiStatusElement) return;

    const apiConfig = this.state.apiConfig;
    const isConfigured = apiConfig.model && apiConfig.apiUrl && apiConfig.apiKey;
    
    const statusIcon = apiStatusElement.querySelector('.material-icons');
    const statusText = apiStatusElement.querySelector('span');
    
    // 移除所有状态类
    apiStatusElement.classList.remove('ok', 'warning', 'error');
    
    if (isConfigured) {
      apiStatusElement.classList.add('ok');
      statusIcon.textContent = 'check_circle';
      statusText.textContent = 'API已配置';
    } else {
      apiStatusElement.classList.add('warning');
      statusIcon.textContent = 'warning';
      statusText.textContent = 'API未设置';
    }
  }

  updateUrlStatus() {
    const urlStatusElement = document.getElementById('url-status');
    if (!urlStatusElement) return;

    const urlCount = Utils.Url.countUrls(this.state.urlText);
    
    const statusIcon = urlStatusElement.querySelector('.material-icons');
    const statusText = urlStatusElement.querySelector('span');
    
    // 移除所有状态类
    urlStatusElement.classList.remove('ok', 'warning', 'error');
    
    if (urlCount > 0) {
      urlStatusElement.classList.add('ok');
      statusIcon.textContent = 'link';
      statusText.textContent = `URL: ${urlCount}`;
    } else {
      urlStatusElement.classList.add('warning');
      statusIcon.textContent = 'link_off';
      statusText.textContent = 'URL: 0';
    }
  }

  async startProcessing() {
    if (this.state.isProcessing) return;

    try {
      // 判断是否为重新获取文章还是处理已选择的文章
      const selectedArticles = Components.ArticleList.getSelectedArticles();
      
      if (selectedArticles.length > 0) {
        // 处理已选择的文章
        await this.processSelectedArticles();
        return;
      }

      // 验证配置
      if (!this.validateConfig()) return;

      this.state.setProcessing(true);
      Components.Log.log('开始处理流程...');
      Components.Progress.reset();

      // 更新API配置
      this.state.updateApiConfig();
      await window.electronAPI.updateApiConfig(this.state.apiConfig);

      // 第一阶段：读取URL
      await this.loadUrls();

      // 第二阶段：获取文章列表
      await this.fetchArticles();

    } catch (error) {
      Components.Log.error(`处理失败: ${error.message}`);
      this.state.setProcessing(false);
    }
  }

  validateConfig() {
    // 验证输入数据
    if (!this.state.urlText || this.state.urlText.trim().length === 0) {
      Components.Modal.show('general', {
        title: '错误',
        body: '请先输入URL列表',
        buttons: [{ text: '确定', className: 'btn-primary', onClick: () => Components.Modal.hide('general') }]
      });
      return false;
    }
    
    const urlCount = Utils.Url.countUrls(this.state.urlText);
    if (urlCount === 0) {
      Components.Modal.show('general', {
        title: '错误',
        body: '输入的文本中没有找到有效的URL',
        buttons: [{ text: '确定', className: 'btn-primary', onClick: () => Components.Modal.hide('general') }]
      });
      return false;
    }

    if (!Utils.Validation.isValidApiConfig(this.state.apiConfig)) {
      Components.Modal.show('general', {
        title: '错误',
        body: '请检查API配置信息是否完整',
        buttons: [{ text: '确定', className: 'btn-primary', onClick: () => Components.Modal.hide('general') }]
      });
      return false;
    }

    return true;
  }

  async loadUrls() {
    try {
      Components.Progress.updateProgress(1, 10, '正在读取URL...');
      
      // 从文本解析
      const urls = Utils.Url.parseUrlsFromText(this.state.urlText);
      if (urls.length === 0) {
        throw new Error('文本中没有找到有效的URL');
      }
      
      this.state.urls = urls;
      
      Components.Log.success(`成功获取 ${urls.length} 个URL`);
      Components.Progress.updateProgress(2, 10, 'URL获取完成');
      
    } catch (error) {
      throw new Error(`获取URL失败: ${error.message}`);
    }
  }

  async fetchArticles() {
    try {
      Components.Progress.updateProgress(3, 10, '正在获取文章列表...');
      
      // 清空现有文章列表
      Components.ArticleList.clearArticles();
      
      const allArticles = [];
      const failedUrls = [];
      const total = this.state.urls.length;

      for (let i = 0; i < total; i++) {
        const url = this.state.urls[i];
        const progress = 3 + (i / total) * 5; // 进度从3到8
        
        Components.Progress.updateProgress(progress, 10, `正在处理: ${url}`);
        Components.Log.log(`正在获取文章列表: ${url}`);

        try {
          const result = await window.electronAPI.getArticlesFromSite(url);
          
          if (result.success && result.articles.length > 0) {
            allArticles.push(...result.articles);
            // 实时添加到文章列表中
            Components.ArticleList.addArticles(result.articles);
            Components.Log.success(`从 ${url} 获取到 ${result.articles.length} 篇文章`);
          } else {
            // 区分真正的失败和AI响应无文章的情况
            if (result.error && !result.error.includes('无文章') && !result.error.includes('没有找到')) {
              failedUrls.push(url);
              Components.Log.error(`获取失败: ${url} - ${result.error}`);
            } else {
              Components.Log.log(`获取结果: ${url} - 无符合条件的文章`);
            }
          }
        } catch (error) {
          failedUrls.push(url);
          Components.Log.error(`获取失败: ${url} - ${error.message}`);
        }

        // 添加小延迟避免过快请求
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      this.state.allArticles = allArticles;
      this.retryData.failedUrls = failedUrls;
      this.retryData.allUrls = this.state.urls;

      Components.Progress.updateProgress(8, 10, '文章列表获取完成');

      // 处理失败的URL
      if (failedUrls.length > 0) {
        await this.handleFailedUrls(allArticles, failedUrls);
      } else {
        // 没有失败的URL，正常结束
        if (allArticles.length === 0) {
          Components.Log.warning('没有找到符合条件的文章');
        } else {
          Components.Log.success(`总共获取到 ${allArticles.length} 篇文章`);
        }
        Components.Progress.updateProgress(10, 10, allArticles.length > 0 ? '请选择要处理的文章' : '获取完成');
        this.state.setProcessing(false);
      }

    } catch (error) {
      throw new Error(`获取文章列表失败: ${error.message}`);
    }
  }

  async processSelectedArticles() {
    const selectedArticles = Components.ArticleList.getSelectedArticles();
    
    if (!selectedArticles || selectedArticles.length === 0) {
      Components.Log.error('请先选择要处理的文章');
      return;
    }

    try {
      this.state.setProcessing(true);
      Components.Progress.updateProgress(0, 100, '开始处理选中的文章...');
      Components.Log.success(`开始处理 ${selectedArticles.length} 篇文章`);
      
      // 更新state中的选择文章
      this.state.selectedArticles = selectedArticles;
      
      const total = selectedArticles.length;
      this.state.processedArticles = [];
      this.state.failedArticles = [];

      for (let i = 0; i < total; i++) {
        const article = selectedArticles[i];
        const progress = ((i + 1) / total) * 100;
        
        Components.Progress.updateProgress(progress, 100, `正在处理: ${article.title}`);
        Components.Log.log(`正在处理文章 ${i + 1}/${total}: ${article.title}`);

        try {
          // 调用Electron API处理单篇文章
          const result = await window.electronAPI.processArticle(article);
          
          if (result.success && result.article) {
            // 使用处理后的文章数据，更新原文章内容
            const processedArticle = {
              ...article,
              title: result.article.title || article.title,
              content: result.article.content || '无内容',
              date: result.article.date || article.date,
              origin: result.article.origin || article.origin,
              tag: result.article.tag || '网络安全及其他',
              processedAt: new Date().toISOString()
            };
            
            this.state.processedArticles.push(processedArticle);
            Components.Log.success(`文章处理成功: ${result.article.title || article.title}`);
          } else {
            this.state.failedArticles.push({
              ...article,
              error: result.error || '处理失败',
              failedAt: new Date().toISOString()
            });
            Components.Log.error(`文章处理失败: ${article.title} - ${result.error || '处理失败'}`);
          }
        } catch (error) {
          this.state.failedArticles.push({
            ...article,
            error: error.message,
            failedAt: new Date().toISOString()
          });
          Components.Log.error(`文章处理异常: ${article.title} - ${error.message}`);
        }

        // 添加小延迟避免过快请求
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 处理完成总结
      Components.Progress.updateProgress(100, 100, '文章处理完成');
      const successCount = this.state.processedArticles.length;
      const failCount = this.state.failedArticles.length;
      
      Components.Log.success(`文章处理完成！成功: ${successCount} 篇，失败: ${failCount} 篇`);
      
      // 如果有失败的文章，循环重试直到没有失败或用户放弃
      await this.handleFailedArticlesWithRetry();

      // 询问是否保存结果
      const finalSuccessCount = this.state.processedArticles.length;
      if (finalSuccessCount > 0) {
        await this.offerSaveResults();
      }

    } catch (error) {
      Components.Log.error(`处理文章时发生错误: ${error.message}`);
    } finally {
      this.state.setProcessing(false);
    }
  }

  async handleFailedUrls(allArticles, failedUrls) {
    if (failedUrls.length === 0) return;

    Components.Log.warning(`有 ${failedUrls.length} 个URL获取失败`);
    
    if (allArticles.length === 0) {
      // 如果没有获取到任何文章，只提供重试选项
      return new Promise((resolve) => {
        Components.Modal.show('general', {
          title: '获取失败',
          body: `所有URL都获取失败了。是否要重试失败的URL？\n\n失败的URL:\n${failedUrls.slice(0, 3).join('\n')}${failedUrls.length > 3 ? '\n...' : ''}`,
          buttons: [
            { 
              text: '取消', 
              className: 'btn-secondary', 
              onClick: () => {
                Components.Modal.hide('general');
                this.state.setProcessing(false);
                resolve();
              }
            },
            { 
              text: '重试', 
              className: 'btn-primary', 
              onClick: async () => {
                Components.Modal.hide('general');
                await this.retryFailedUrls(failedUrls, allArticles);
                resolve();
              }
            }
          ]
        });
      });
    } else {
      // 有部分成功，提供继续处理和重试失败URL两个选项
      return new Promise((resolve) => {
        Components.Modal.show('general', {
          title: '部分获取失败',
          body: `已成功获取 ${allArticles.length} 篇文章，但有 ${failedUrls.length} 个URL失败。\n\n失败的URL:\n${failedUrls.slice(0, 3).join('\n')}${failedUrls.length > 3 ? '\n...' : ''}\n\n请选择下一步操作：`,
          buttons: [
            { 
              text: '取消', 
              className: 'btn-secondary', 
              onClick: () => {
                Components.Modal.hide('general');
                this.state.setProcessing(false);
                resolve();
              }
            },
            { 
              text: '重试失败URL', 
              className: 'btn-secondary', 
              onClick: async () => {
                Components.Modal.hide('general');
                await this.retryFailedUrls(failedUrls, allArticles);
                resolve();
              }
            },
            { 
              text: '继续处理', 
              className: 'btn-primary', 
              onClick: () => {
                Components.Modal.hide('general');
                Components.Log.success(`继续处理已获取的 ${allArticles.length} 篇文章`);
                resolve();
              }
            }
          ]
        });
      });
    }
  }

  async retryFailedUrls(failedUrls, allArticles) {
    try {
      Components.Log.log(`开始重试 ${failedUrls.length} 个失败的URL`);
      Components.Progress.updateProgress(0, 100, '重试失败的URL...');
      
      const retryArticles = [];
      const stillFailedUrls = [];
      const total = failedUrls.length;

      for (let i = 0; i < total; i++) {
        const url = failedUrls[i];
        const progress = ((i + 1) / total) * 100;
        
        Components.Progress.updateProgress(progress, 100, `重试: ${url}`);
        Components.Log.log(`重试URL: ${url}`);

        try {
          const result = await window.electronAPI.getArticlesFromSite(url);
          
          if (result.success && result.articles.length > 0) {
            retryArticles.push(...result.articles);
            // 实时添加到文章列表中
            Components.ArticleList.addArticles(result.articles);
            Components.Log.success(`重试成功: ${url} - 获取到 ${result.articles.length} 篇文章`);
          } else {
            // 过滤掉AI响应无文章的情况（这通常不是错误）
            if (result.error && !result.error.includes('无文章') && !result.error.includes('没有找到')) {
              stillFailedUrls.push(url);
              Components.Log.error(`重试失败: ${url} - ${result.error}`);
            } else {
              Components.Log.log(`重试结果: ${url} - 无符合条件的文章`);
            }
          }
        } catch (error) {
          stillFailedUrls.push(url);
          Components.Log.error(`重试异常: ${url} - ${error.message}`);
        }

        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 合并重试成功的文章到state
      this.state.allArticles.push(...retryArticles);
      
      if (retryArticles.length > 0) {
        Components.Log.success(`重试完成！新获取到 ${retryArticles.length} 篇文章`);
      }
      
      // 过滤出真正失败的URL（排除AI响应无文章的情况）
      if (stillFailedUrls.length > 0) {
        Components.Log.warning(`仍有 ${stillFailedUrls.length} 个URL失败`);
        
        // 递归处理仍然失败的URL
        const currentAllArticles = [...allArticles, ...retryArticles];
        await this.handleFailedUrls(currentAllArticles, stillFailedUrls);
      } else {
        // 所有重试都成功了，继续正常流程
        const totalArticles = allArticles.length + retryArticles.length;
        if (totalArticles > 0) {
          Components.Log.success(`总共获取到 ${totalArticles} 篇文章`);
          Components.Progress.updateProgress(100, 100, '请选择要处理的文章');
          this.state.setProcessing(false);
        } else {
          Components.Log.warning('没有获取到任何文章');
          this.state.setProcessing(false);
        }
      }

    } catch (error) {
      Components.Log.error(`重试过程中发生错误: ${error.message}`);
      this.state.setProcessing(false);
    }
  }

  async handleFailedArticlesWithRetry() {
    // 循环处理失败的文章直到没有失败或用户放弃
    while (this.state.failedArticles.length > 0) {
      const shouldRetry = await this.handleFailedArticles();
      if (!shouldRetry) {
        // 用户选择跳过，结束重试循环
        break;
      }
      
      // 执行重试
      await this.retryFailedArticles();
      
      // 输出重试后的状态
      const remainingFailedCount = this.state.failedArticles.length;
      if (remainingFailedCount > 0) {
        Components.Log.warning(`重试完成，仍有 ${remainingFailedCount} 篇文章处理失败`);
      } else {
        Components.Log.success('所有文章重试成功！');
        break;
      }
    }
  }

  async handleFailedArticles() {
    if (this.state.failedArticles.length === 0) return false;

    return new Promise((resolve) => {
      const failedCount = this.state.failedArticles.length;
      const failedTitles = this.state.failedArticles
        .slice(0, 3)
        .map(article => `• ${article.title}`)
        .join('\n');
      const moreText = this.state.failedArticles.length > 3 ? '\n• ...' : '';
      
      Components.Modal.show('general', {
        title: '处理失败的文章',
        body: `有 ${failedCount} 篇文章处理失败。\n\n失败的文章：\n${failedTitles}${moreText}\n\n是否要重试这些文章？`,
        buttons: [
          { 
            text: '跳过', 
            className: 'btn-secondary', 
            onClick: () => {
              Components.Modal.hide('general');
              Components.Log.log('已跳过失败的文章');
              resolve(false);
            }
          },
          { 
            text: '重试', 
            className: 'btn-primary', 
            onClick: () => {
              Components.Modal.hide('general');
              resolve(true);
            }
          }
        ]
      });
    });
  }

  async retryFailedArticles() {
    const failedArticles = [...this.state.failedArticles];
    this.state.failedArticles = [];
    
    Components.Log.log(`开始重试 ${failedArticles.length} 篇失败的文章`);
    Components.Progress.updateProgress(0, 100, '正在重试失败的文章...');
    
    const total = failedArticles.length;
    for (let i = 0; i < total; i++) {
      const article = failedArticles[i];
      const progress = ((i + 1) / total) * 100;
      
      Components.Progress.updateProgress(progress, 100, `重试文章: ${article.title}`);
      Components.Log.log(`重试文章 ${i + 1}/${total}: ${article.title}`);
      
      try {
        const result = await window.electronAPI.processArticle(article);
        
        if (result.success && result.article) {
          // 使用处理后的文章数据，更新原文章内容
          const processedArticle = {
            ...article,
            title: result.article.title || article.title,
            content: result.article.content || '无内容',
            date: result.article.date || article.date,
            origin: result.article.origin || article.origin,
            tag: result.article.tag || '网络安全及其他',
            processedAt: new Date().toISOString()
          };
          
          this.state.processedArticles.push(processedArticle);
          Components.Log.success(`重试成功: ${result.article.title || article.title}`);
        } else {
          this.state.failedArticles.push({
            ...article,
            error: result.error || '处理失败',
            retryFailedAt: new Date().toISOString()
          });
          Components.Log.error(`重试仍失败: ${article.title} - ${result.error || '处理失败'}`);
        }
      } catch (error) {
        this.state.failedArticles.push({
          ...article,
          error: error.message,
          retryFailedAt: new Date().toISOString()
        });
        Components.Log.error(`重试异常: ${article.title} - ${error.message}`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    Components.Progress.updateProgress(100, 100, '重试完成');
  }

  async offerSaveResults() {
    const successCount = this.state.processedArticles.length;
    const failCount = this.state.failedArticles.length;
    const totalSelected = this.state.selectedArticles.length;
    
    return new Promise((resolve) => {
      let bodyText = `处理完成！\n\n总计选择: ${totalSelected} 篇文章\n成功处理: ${successCount} 篇\n处理失败: ${failCount} 篇`;
      
      if (failCount > 0) {
        bodyText += `\n\n失败的文章也会包含在保存结果中，方便后续查看和处理。`;
      }
      
      bodyText += `\n\n是否要保存处理结果？`;
      
      Components.Modal.show('general', {
        title: '保存结果',
        body: bodyText,
        buttons: [
          { 
            text: '不保存', 
            className: 'btn-secondary', 
            onClick: () => {
              Components.Modal.hide('general');
              Components.Log.log('已完成处理，未保存结果');
              resolve();
            }
          },
          { 
            text: '保存结果', 
            className: 'btn-primary', 
            onClick: async () => {
              Components.Modal.hide('general');
              await this.saveResults();
              resolve();
            }
          }
        ]
      });
    });
  }

  async saveResults() {
    try {
      Components.Log.log('正在保存处理结果...');
      
      const results = {
        processedArticles: this.state.processedArticles,
        failedArticles: this.state.failedArticles,
        summary: {
          totalSelected: this.state.selectedArticles.length,
          successCount: this.state.processedArticles.length,
          failCount: this.state.failedArticles.length,
          processedAt: new Date().toISOString()
        }
      };
      
      const saved = await window.electronAPI.saveResults(results);
      
      if (saved.success) {
        Components.Log.success(`结果已保存到: ${saved.filePath}`);
      } else {
        Components.Log.error(`保存失败: ${saved.error}`);
      }
      
    } catch (error) {
      Components.Log.error(`保存结果时发生错误: ${error.message}`);
    }
  }

  restoreConfig() {
    // 恢复API配置
    const savedConfig = Utils.Storage.load('apiConfig', {});
    Object.keys(savedConfig).forEach(key => {
      const input = document.getElementById(key.replace(/([A-Z])/g, '-$1').toLowerCase());
      if (input && savedConfig[key]) {
        input.value = savedConfig[key];
        this.state.apiConfig[key] = savedConfig[key];
      }
    });

    // 恢复文本输入内容
    const savedUrlText = Utils.Storage.load('urlText', '');
    if (savedUrlText) {
      const urlTextInput = document.getElementById('url-text-input');
      if (urlTextInput) {
        urlTextInput.value = savedUrlText;
        this.state.urlText = savedUrlText;
        this.updateUrlCount();
      }
    }

    this.state.updateUI();
    this.updateStatusIndicators();
  }

  saveConfig() {
    Utils.Storage.save('apiConfig', this.state.apiConfig);
    Utils.Storage.save('urlText', this.state.urlText);
  }
}

// 初始化应用
window.addEventListener('DOMContentLoaded', () => {
  window.app = new NewsScraperApp();
});

// 导出应用实例供调试使用
window.NewsScraperApp = NewsScraperApp; 