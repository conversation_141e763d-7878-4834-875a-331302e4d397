@echo off
chcp 65001 >nul
echo 新闻爬取工具 - Electron版本
echo ================================

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未安装Node.js，请先安装Node.js 16.0或更高版本
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

:: 检查是否存在node_modules
if not exist "node_modules" (
    echo 首次运行，正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

:: 启动应用
echo 正在启动应用...
npm start

if %errorlevel% neq 0 (
    echo 应用启动失败
    pause
) 