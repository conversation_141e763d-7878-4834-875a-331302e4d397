/* CSS变量定义 - 主题系统 */
:root {
  /* 主色调 */
  --color-primary: #1976d2;
  --color-primary-light: #42a5f5;
  --color-primary-dark: #1565c0;
  
  /* 次要色调 */
  --color-secondary: #424242;
  --color-secondary-light: #6d6d6d;
  --color-secondary-dark: #212121;
  
  /* 状态色彩 */
  --color-success: #4caf50;
  --color-warning: #ff9800;
  --color-error: #f44336;
  --color-info: #2196f3;
  
  /* 背景色彩 */
  --color-background: #f5f5f5;
  --color-surface: #ffffff;
  --color-surface-variant: #fafafa;
  
  /* 文字色彩 */
  --color-text: #212121;
  --color-text-secondary: #757575;
  --color-text-disabled: #bdbdbd;
  --color-text-inverse: #ffffff;
  
  /* 边框色彩 */
  --color-border: #e0e0e0;
  --color-border-light: #f0f0f0;
  --color-border-dark: #d0d0d0;
  
  /* 阴影 */
  --shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  
  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 字体 */
  --font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height: 1.5;
  --line-height-loose: 1.8;
  
  /* 过渡效果 */
  --transition-fast: 0.15s ease;
  --transition: 0.25s ease;
  --transition-slow: 0.35s ease;
}

/* 深色主题 */
[data-theme="dark"] {
  /* 主色调 - 深色模式下稍微调亮 */
  --color-primary: #42a5f5;
  --color-primary-light: #64b5f6;
  --color-primary-dark: #1976d2;
  
  /* 次要色调 */
  --color-secondary: #666666;
  --color-secondary-light: #888888;
  --color-secondary-dark: #444444;
  
  /* 状态色彩 - 深色模式优化 */
  --color-success: #66bb6a;
  --color-warning: #ffa726;
  --color-error: #ef5350;
  --color-info: #42a5f5;
  
  /* 背景色彩 - 深色 */
  --color-background: #121212;
  --color-surface: #1e1e1e;
  --color-surface-variant: #2a2a2a;
  
  /* 文字色彩 - 深色模式 */
  --color-text: #ffffff;
  --color-text-secondary: #b3b3b3;
  --color-text-disabled: #666666;
  --color-text-inverse: #121212;
  
  /* 边框色彩 - 深色 */
  --color-border: #333333;
  --color-border-light: #2a2a2a;
  --color-border-dark: #444444;
  
  /* 阴影 - 深色模式调整 */
  --shadow-1: 0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.6);
  --shadow-2: 0 3px 6px rgba(0, 0, 0, 0.6), 0 3px 6px rgba(0, 0, 0, 0.7);
  --shadow-3: 0 10px 20px rgba(0, 0, 0, 0.8), 0 6px 6px rgba(0, 0, 0, 0.7);
}

/* 高对比度主题 */
[data-theme="high-contrast"] {
  --color-primary: #0066ff;
  --color-background: #000000;
  --color-surface: #000000;
  --color-text: #ffffff;
  --color-border: #ffffff;
}

/* 主题切换动画 */
* {
  transition: 
    background-color var(--transition),
    border-color var(--transition),
    color var(--transition),
    box-shadow var(--transition);
}

/* 减少动画的偏好设置 */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
} 