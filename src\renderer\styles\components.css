/* 组件样式文件 */

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
  white-space: nowrap;
  min-height: 36px;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-1);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  box-shadow: var(--shadow-2);
  transform: translateY(-1px);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-1);
}

.btn-secondary {
  background-color: var(--color-surface);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-surface-variant);
  border-color: var(--color-border-dark);
}

.btn-icon {
  background-color: transparent;
  color: var(--color-text-secondary);
  padding: var(--spacing-xs);
  min-width: 32px;
  min-height: 32px;
  border-radius: 50%;
}

.btn-icon:hover:not(:disabled) {
  background-color: var(--color-surface-variant);
  color: var(--color-text);
}

/* 输入框组件 */
.input-field {
  padding: var(--spacing-sm) var(--spacing);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: var(--font-size);
  background-color: var(--color-surface);
  color: var(--color-text);
  transition: all var(--transition);
  width: 100%;
}

.input-field:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.input-field:disabled {
  background-color: var(--color-surface-variant);
  color: var(--color-text-disabled);
  cursor: not-allowed;
}

.input-field::placeholder {
  color: var(--color-text-disabled);
}

/* 模态框组件 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 1;
  visibility: visible;
  transition: all var(--transition);
}

/* 历史记录模态框层级更高，避免被设置框挡住 */
#history-modal {
  z-index: 1100;
}

.modal-overlay.hidden {
  opacity: 0;
  visibility: hidden;
}

.modal-content {
  background-color: var(--color-surface);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-3);
  max-width: 90vw;
  max-height: 90vh;
  width: 600px;
  display: flex;
  flex-direction: column;
  transform: scale(1);
  transition: transform var(--transition);
}

.modal-overlay.hidden .modal-content {
  transform: scale(0.9);
}

.modal-content.large {
  width: 80vw;
  max-width: 900px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing);
  border-bottom: 1px solid var(--color-border);
}

.modal-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text);
}

.modal-body {
  flex: 1;
  padding: var(--spacing);
  overflow: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing);
  border-top: 1px solid var(--color-border);
}

/* 文章选择器组件 */
.article-selector-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
  margin-bottom: var(--spacing);
}

.search-box {
  position: relative;
}

.search-box input {
  padding-right: 40px;
}

.search-box .material-icons {
  position: absolute;
  right: var(--spacing);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-secondary);
  pointer-events: none;
}

.selector-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.selection-count {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.articles-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
}

.article-item {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing);
  border-bottom: 1px solid var(--color-border-light);
  cursor: pointer;
  transition: background-color var(--transition);
}

.article-item:last-child {
  border-bottom: none;
}

.article-item:hover {
  background-color: var(--color-surface-variant);
}

.article-item.selected {
  background-color: rgba(25, 118, 210, 0.1);
  border-left: 3px solid var(--color-primary);
}

.article-checkbox {
  margin-right: var(--spacing);
  margin-top: 2px;
}

.article-info {
  flex: 1;
}

.article-title {
  font-weight: 500;
  color: var(--color-text);
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-tight);
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* 隐藏类 */
.hidden {
  display: none !important;
}



/* 文本输入区域 */
.text-input-area {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-header label {
  font-weight: 500;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.input-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.text-input-field {
  width: 100%;
  min-height: 120px;
  max-height: 200px;
  padding: var(--spacing);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-family: var(--font-family);
  background-color: var(--color-surface);
  color: var(--color-text);
  transition: all var(--transition);
  resize: vertical;
  line-height: var(--line-height);
}

.text-input-field:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.text-input-field::placeholder {
  color: var(--color-text-disabled);
}

.text-input-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
}

#url-count {
  color: var(--color-text-secondary);
}

.btn-text {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition);
}

.btn-text:hover {
  background-color: rgba(25, 118, 210, 0.1);
}

/* 历史记录相关样式 */
.history-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing);
  margin-bottom: var(--spacing);
}

.history-controls .search-box {
  flex: 1;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
}

.history-item {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing);
  border-bottom: 1px solid var(--color-border-light);
  cursor: pointer;
  transition: background-color var(--transition);
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:hover {
  background-color: var(--color-surface-variant);
}

.history-item.selected {
  background-color: rgba(25, 118, 210, 0.1);
  border-left: 3px solid var(--color-primary);
}

.history-checkbox {
  margin-right: var(--spacing);
  margin-top: 2px;
}

.history-content {
  flex: 1;
}

.history-preview {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-tight);
  white-space: pre-wrap;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

.history-date {
  background-color: var(--color-surface-variant);
  padding: 2px var(--spacing-xs);
  border-radius: var(--border-radius-sm);
}

.history-count {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  padding: 2px var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  font-weight: 500;
}

.empty-history {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--color-text-secondary);
}

.empty-history .material-icons {
  font-size: 48px;
  margin-bottom: var(--spacing);
  opacity: 0.5;
}

/* 状态指示器 */
.status-indicators {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  margin-right: var(--spacing);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-surface-variant);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  transition: all var(--transition);
}

.status-item.ok {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: var(--color-success);
  color: var(--color-success);
}

.status-item.warning {
  background-color: rgba(255, 152, 0, 0.1);
  border-color: var(--color-warning);
  color: var(--color-warning);
}

.status-item.error {
  background-color: rgba(244, 67, 54, 0.1);
  border-color: var(--color-error);
  color: var(--color-error);
}

.status-item .material-icons {
  font-size: 16px;
}

/* 设置页面样式 */
.settings-tabs {
  display: flex;
  background-color: var(--color-surface-variant);
  border-radius: var(--border-radius);
  padding: 4px;
  margin-bottom: var(--spacing-lg);
}

.settings-tab {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing);
  border: none;
  border-radius: var(--border-radius-sm);
  background-color: transparent;
  color: var(--color-text-secondary);
  font-size: var(--font-size);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition);
}

.settings-tab:hover {
  background-color: var(--color-surface);
  color: var(--color-text);
}

.settings-tab.active {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-1);
}

.settings-content {
  display: none;
}

.settings-content.active {
  display: block;
}

.settings-section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border-light);
}

.settings-section-title .material-icons {
  font-size: 20px;
  color: var(--color-primary);
}

/* 控制区域简化样式 */
.control-section {
  display: flex;
  justify-content: center;
  padding: var(--spacing-xl);
  background-color: var(--color-surface);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-1);
  border: 1px solid var(--color-border);
  flex-shrink: 0;
}

.control-section .btn-primary {
  padding: var(--spacing) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-width: 200px;
}

/* 响应式设计更新 */
@media (max-width: 768px) {
  .status-indicators {
    display: none;
  }
  
  .header-actions {
    gap: var(--spacing-sm);
  }
  
  .settings-tabs {
    flex-direction: column;
    gap: 4px;
  }
  
  .status-item {
    font-size: var(--font-size-xs);
    padding: 2px var(--spacing-xs);
  }
}

/* 主页面文章选择区域 */
.articles-section {
  flex: 1;
  background-color: var(--color-surface);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-1);
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.articles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing);
  border-bottom: 1px solid var(--color-border-light);
  flex-wrap: wrap;
  gap: var(--spacing);
}

.articles-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  flex-wrap: wrap;
}

.articles-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.articles-controls .btn-primary {
  min-width: 120px;
  font-weight: 600;
  box-shadow: var(--shadow-2);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border: 1px solid var(--color-primary);
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
}

.articles-controls .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.articles-controls .btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-3);
  background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
  border-color: var(--color-primary-dark);
}

.articles-controls .btn-primary:hover:not(:disabled)::before {
  left: 100%;
}

.articles-controls .btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-1);
}

.articles-controls .btn-primary:disabled {
  background: linear-gradient(135deg, var(--color-surface-variant), var(--color-surface));
  color: var(--color-text-disabled);
  border-color: var(--color-border);
  transform: none;
  box-shadow: none;
}

.articles-controls .btn-primary:disabled::before {
  display: none;
}

.articles-controls .btn-secondary {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
}

.articles-controls .btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition);
}

.articles-controls .btn-secondary:hover:not(:disabled) {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-1);
}

.articles-controls .btn-secondary:hover:not(:disabled)::before {
  left: 100%;
}

.articles-controls .btn-secondary:active:not(:disabled) {
  transform: translateY(0);
}

.articles-controls .selection-count {
  background: linear-gradient(135deg, var(--color-surface-variant), var(--color-surface));
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border-light);
  white-space: nowrap;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.articles-controls .selection-count span {
  color: var(--color-primary);
  font-weight: 600;
}

.articles-search-container {
  padding: var(--spacing);
  border-bottom: 1px solid var(--color-border-light);
}

.articles-list-main {
  flex: 1;
  overflow: auto;
  min-height: 0;
  position: relative;
}

.empty-articles {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  text-align: center;
  color: var(--color-text-secondary);
  height: 100%;
  min-height: 300px;
}

.empty-articles .material-icons {
  font-size: 64px;
  margin-bottom: var(--spacing);
  opacity: 0.5;
  color: var(--color-text-disabled);
}

.empty-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  color: var(--color-text-secondary);
}

.empty-hint {
  font-size: var(--font-size-sm);
  color: var(--color-text-disabled);
  max-width: 400px;
}

/* 文章表格样式 */
.articles-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--color-surface);
  font-size: var(--font-size-sm);
  min-width: 600px;
  border: 1px solid var(--color-border);
}

.articles-table thead {
  background-color: var(--color-surface-variant);
  border-bottom: 2px solid var(--color-border);
}

.articles-table th {
  padding: var(--spacing) var(--spacing-sm);
  text-align: left;
  font-weight: 600;
  color: var(--color-text);
  border-right: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  background-color: var(--color-surface-variant);
  z-index: 1;
}

.articles-table th:last-child {
  border-right: none;
}

.articles-table .checkbox-col {
  width: 50px;
  text-align: center;
}

.articles-table .title-col {
  width: 30%;
  min-width: 180px;
}

.articles-table .raw-title-col {
  width: 25%;
  min-width: 160px;
}

.articles-table .date-col {
  width: 15%;
  min-width: 120px;
}

.articles-table .tag-col {
  width: 12%;
  min-width: 100px;
}

.articles-table .origin-col {
  width: 18%;
  min-width: 130px;
}

.articles-table tbody tr {
  border-bottom: 1px solid var(--color-border-light);
  transition: background-color var(--transition);
  cursor: pointer;
}

.articles-table tbody tr:hover {
  background-color: var(--color-surface-variant);
}

.articles-table tbody tr.selected {
  background-color: rgba(25, 118, 210, 0.1);
  border-left: 3px solid var(--color-primary);
}

.articles-table tbody tr.selected:hover {
  background-color: rgba(25, 118, 210, 0.15);
}

.articles-table td {
  padding: var(--spacing-sm);
  border-right: 1px solid var(--color-border-light);
  vertical-align: top;
}

.articles-table td:last-child {
  border-right: none;
}

.checkbox-cell {
  text-align: center;
  width: 50px;
}

.table-checkbox {
  margin: 0;
  cursor: pointer;
  transform: scale(1.1);
}

.title-cell .article-title {
  font-weight: 500;
  color: var(--color-text);
  line-height: var(--line-height-tight);
  word-break: break-word;
}

.raw-title-cell .article-raw-title {
  color: var(--color-text-secondary);
  font-style: italic;
  line-height: var(--line-height-tight);
  word-break: break-word;
}

.date-cell .article-date {
  color: var(--color-text-secondary);
  white-space: nowrap;
}

.tag-cell .article-tag {
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  background-color: var(--color-surface-variant);
  padding: 2px var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  display: inline-block;
  white-space: nowrap;
  font-weight: 500;
  border: 1px solid var(--color-border-light);
}

.origin-cell .article-origin {
  color: var(--color-text-secondary);
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .articles-table .raw-title-col {
    display: none;
  }
  
  .articles-table .raw-title-cell {
    display: none;
  }
  
  .articles-table .title-col {
    width: 40%;
  }
  
  .articles-table .date-col {
    width: 20%;
  }
  
  .articles-table .tag-col {
    width: 15%;
  }
  
  .articles-table .origin-col {
    width: 25%;
  }
}

@media (max-width: 768px) {
  .articles-table {
    font-size: var(--font-size-xs);
  }
  
  .articles-table th,
  .articles-table td {
    padding: var(--spacing-xs);
  }
  
  .articles-table .date-col {
    display: none;
  }
  
  .articles-table .date-cell {
    display: none;
  }
  
  .articles-table .tag-col {
    display: none;
  }
  
  .articles-table .tag-cell {
    display: none;
  }
  
  .articles-table .title-col {
    width: 60%;
  }
  
  .articles-table .origin-col {
    width: 40%;
  }
}

/* 移除原有的文章项样式，保留表格外的样式 */
.articles-list-main .article-item,
.articles-list-main .article-checkbox,
.articles-list-main .article-info,
.articles-list-main .article-title,
.articles-list-main .article-raw-title,
.articles-list-main .article-meta,
.articles-list-main .article-meta-item {
  /* 这些样式现在由表格样式替代 */
}

/* 响应式设计更新 */
@media (max-width: 768px) {
  .articles-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .articles-controls {
    justify-content: space-between;
    gap: var(--spacing-sm);
  }
  
  .articles-actions {
    flex: 1;
    justify-content: flex-start;
    gap: var(--spacing-xs);
  }

  .articles-controls .btn-secondary {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs);
    min-width: auto;
  }

  .articles-controls .btn-primary {
    min-width: auto;
    flex: 1;
    max-width: 140px;
  }

  .articles-controls .selection-count {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs);
  }
  
  .empty-articles {
    padding: var(--spacing-lg);
    min-height: 200px;
  }
  
  .empty-articles .material-icons {
    font-size: 48px;
  }
} 