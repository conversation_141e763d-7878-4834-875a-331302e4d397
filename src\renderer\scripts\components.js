// 组件管理模块

/**
 * 模态框管理器
 */
class ModalManager {
  constructor() {
    this.modals = new Map();
    this.init();
  }

  init() {
    // 初始化通用模态框
    this.registerModal('general', {
      element: document.getElementById('modal-overlay'),
      titleElement: document.getElementById('modal-title'),
      bodyElement: document.getElementById('modal-body'),
      footerElement: document.getElementById('modal-footer'),
      closeButton: document.getElementById('modal-close')
    });

    // 初始化历史记录模态框
    this.registerModal('history', {
      element: document.getElementById('history-modal'),
      closeButton: document.getElementById('history-modal-close')
    });

    // 初始化设置模态框
    this.registerModal('settings', {
      element: document.getElementById('settings-modal'),
      closeButton: document.getElementById('settings-modal-close')
    });

    // 绑定关闭事件
    this.bindCloseEvents();
  }

  registerModal(name, config) {
    this.modals.set(name, config);
  }

  show(modalName, options = {}) {
    const modal = this.modals.get(modalName);
    if (!modal) {
      console.error(`模态框未找到: ${modalName}`);
      return;
    }

    if (modalName === 'general') {
      this.showGeneralModal(modal, options);
    } else if (modalName === 'history') {
      this.showHistoryModal(modal, options);
    } else if (modalName === 'settings') {
      this.showSettingsModal(modal, options);
    }

    Utils.DOM.toggleVisible(modal.element, true);
    
    // 焦点管理
    setTimeout(() => {
      const firstInput = modal.element.querySelector('input, button');
      if (firstInput) firstInput.focus();
    }, 100);
  }

  hide(modalName) {
    const modal = this.modals.get(modalName);
    if (modal) {
      Utils.DOM.toggleVisible(modal.element, false);
    }
  }

  showGeneralModal(modal, { title, body, buttons = [] }) {
    if (modal.titleElement) modal.titleElement.textContent = title || '';
    if (modal.bodyElement) modal.bodyElement.innerHTML = body || '';
    
    if (modal.footerElement) {
      modal.footerElement.innerHTML = '';
      buttons.forEach(btn => {
        const button = Utils.DOM.createElement('button', `btn ${btn.className || 'btn-secondary'}`, btn.text);
        if (btn.onClick) button.addEventListener('click', btn.onClick);
        modal.footerElement.appendChild(button);
      });
    }
  }

  showHistoryModal(modal, { onConfirm }) {
    this.renderHistoryList();
    this.setupHistoryModalEvents(onConfirm);
  }

  renderHistoryList(searchTerm = '') {
    const historyList = document.getElementById('history-list');
    const searchInput = document.getElementById('history-search');
    
    const history = Utils.History.searchHistory(searchTerm);
    
    historyList.innerHTML = '';
    
    if (history.length === 0) {
      const emptyState = Utils.DOM.createElement('div', 'empty-history');
      emptyState.innerHTML = `
        <i class="material-icons">history</i>
        <div>暂无历史记录</div>
      `;
      historyList.appendChild(emptyState);
      return;
    }
    
    history.forEach((item) => {
      const historyItem = this.createHistoryItem(item);
      historyList.appendChild(historyItem);
    });

    // 搜索功能
    if (searchInput) {
      searchInput.oninput = Utils.Event.debounce((e) => {
        this.renderHistoryList(e.target.value);
      }, 300);
    }
  }

  createHistoryItem(historyItem) {
    const item = Utils.DOM.createElement('div', 'history-item');
    item.dataset.id = historyItem.id;

    const date = new Date(historyItem.timestamp);
    const formattedDate = date.toLocaleString('zh-CN', {
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    item.innerHTML = `
      <input type="radio" name="history-selection" class="history-checkbox" value="${historyItem.id}">
      <div class="history-content">
        <div class="history-preview">${Utils.Text.escapeHtml(historyItem.preview)}</div>
        <div class="history-meta">
          <span class="history-date">${formattedDate}</span>
          <span class="history-count">${historyItem.urlCount} 个URL</span>
        </div>
      </div>
    `;

    // 点击整个条目选中
    item.addEventListener('click', (e) => {
      if (e.target.type !== 'radio') {
        const radio = item.querySelector('.history-checkbox');
        radio.checked = true;
        this.toggleHistorySelection(item, true);
      }
    });

    // 单选框变化事件
    const radio = item.querySelector('.history-checkbox');
    radio.addEventListener('change', (e) => {
      this.toggleHistorySelection(item, e.target.checked);
    });

    return item;
  }

  toggleHistorySelection(item, selected) {
    // 移除其他选中状态
    document.querySelectorAll('.history-item.selected').forEach(el => {
      el.classList.remove('selected');
    });
    
    if (selected) {
      item.classList.add('selected');
    }
    
    // 更新使用按钮状态
    const useBtn = document.getElementById('use-history');
    if (useBtn) {
      useBtn.disabled = !selected;
    }
  }

  setupHistoryModalEvents(onConfirm) {
    const clearAllBtn = document.getElementById('clear-all-history');
    const useBtn = document.getElementById('use-history');
    const cancelBtn = document.getElementById('cancel-history');

    // 移除旧的事件监听器
    clearAllBtn.replaceWith(clearAllBtn.cloneNode(true));
    useBtn.replaceWith(useBtn.cloneNode(true));
    cancelBtn.replaceWith(cancelBtn.cloneNode(true));

    // 重新获取元素引用
    const newClearAllBtn = document.getElementById('clear-all-history');
    const newUseBtn = document.getElementById('use-history');
    const newCancelBtn = document.getElementById('cancel-history');

    // 清空所有历史
    newClearAllBtn.addEventListener('click', () => {
      this.show('general', {
        title: '确认清空',
        body: '确定要清空所有历史记录吗？此操作不可撤销。',
        buttons: [
          { text: '取消', className: 'btn-secondary', onClick: () => this.hide('general') },
          { 
            text: '确定', 
            className: 'btn-primary', 
            onClick: () => {
              Utils.History.clearHistory();
              this.hide('general');
              this.renderHistoryList();
            }
          }
        ]
      });
    });

    // 使用选中的历史记录
    newUseBtn.addEventListener('click', () => {
      const selectedRadio = document.querySelector('.history-checkbox:checked');
      if (selectedRadio) {
        const historyId = parseInt(selectedRadio.value);
        const history = Utils.History.getHistory();
        const selectedHistory = history.find(item => item.id === historyId);
        
        if (selectedHistory) {
          this.hide('history');
          if (onConfirm) onConfirm(selectedHistory.text);
        }
      }
    });

    // 取消
    newCancelBtn.addEventListener('click', () => {
      this.hide('history');
    });
  }

  showSettingsModal(modal, options = {}) {
    this.setupSettingsEvents();
  }

  setupSettingsEvents() {
    const settingsTabs = document.querySelectorAll('.settings-tab');
    const saveBtn = document.getElementById('save-settings');
    const cancelBtn = document.getElementById('cancel-settings');

    // 移除旧的事件监听器
    settingsTabs.forEach(tab => {
      tab.replaceWith(tab.cloneNode(true));
    });
    saveBtn.replaceWith(saveBtn.cloneNode(true));
    cancelBtn.replaceWith(cancelBtn.cloneNode(true));

    // 重新获取元素引用
    const newSettingsTabs = document.querySelectorAll('.settings-tab');
    const newSaveBtn = document.getElementById('save-settings');
    const newCancelBtn = document.getElementById('cancel-settings');

    // 设置页面标签切换
    newSettingsTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const targetTab = tab.dataset.tab;
        this.switchSettingsTab(targetTab);
      });
    });

    // 保存设置
    newSaveBtn.addEventListener('click', () => {
      this.saveSettings();
    });

    // 取消设置
    newCancelBtn.addEventListener('click', () => {
      this.hide('settings');
    });
  }

  switchSettingsTab(targetTab) {
    // 切换标签样式
    document.querySelectorAll('.settings-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    document.querySelector(`[data-tab="${targetTab}"]`).classList.add('active');

    // 切换内容区域
    document.querySelectorAll('.settings-content').forEach(content => {
      content.classList.remove('active');
    });
    document.querySelector(`.${targetTab}-settings`).classList.add('active');
  }

  saveSettings() {
    // 触发保存事件，让主应用处理
    if (window.app && window.app.saveSettingsFromModal) {
      window.app.saveSettingsFromModal();
    }
    this.hide('settings');
  }

  bindCloseEvents() {
    // 点击遮罩层关闭
    this.modals.forEach((modal, name) => {
      modal.element.addEventListener('click', (e) => {
        if (e.target === modal.element) {
          this.hide(name);
        }
      });

      // 关闭按钮
      if (modal.closeButton) {
        modal.closeButton.addEventListener('click', () => {
          this.hide(name);
        });
      }
    });

    // ESC键关闭
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.modals.forEach((modal, name) => {
          if (!modal.element.classList.contains('hidden')) {
            this.hide(name);
          }
        });
      }
    });
  }
}

/**
 * 进度管理器
 */
class ProgressManager {
  constructor() {
    this.progressFill = document.getElementById('progress-fill');
    this.progressText = document.getElementById('progress-text');
    this.progressPercentage = document.getElementById('progress-percentage');
  }

  updateProgress(current, total, text = '') {
    const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
    
    if (this.progressFill) {
      this.progressFill.style.width = `${percentage}%`;
    }
    
    if (this.progressText && text) {
      this.progressText.textContent = text;
    }
    
    if (this.progressPercentage) {
      this.progressPercentage.textContent = `${percentage}%`;
    }
  }

  reset() {
    this.updateProgress(0, 100, '等待开始...');
  }
}

/**
 * 日志管理器
 */
class LogManager {
  constructor() {
    this.logContent = document.getElementById('log-content');
    this.clearLogBtn = document.getElementById('clear-log');
    this.init();
  }

  init() {
    if (this.clearLogBtn) {
      this.clearLogBtn.addEventListener('click', () => {
        this.clear();
      });
    }
  }

  log(message, type = 'info') {
    if (!this.logContent) return;

    const timestamp = Utils.Date.getCurrentTimestamp();
    const logEntry = Utils.DOM.createElement('div', `log-entry ${type}`);
    logEntry.textContent = `[${timestamp}] ${message}`;
    
    this.logContent.appendChild(logEntry);
    Utils.DOM.scrollToBottom(this.logContent);
  }

  error(message) {
    this.log(message, 'error');
  }

  success(message) {
    this.log(message, 'success');
  }

  warning(message) {
    this.log(message, 'warning');
  }

  clear() {
    if (this.logContent) {
      this.logContent.innerHTML = '';
    }
  }
}

/**
 * 主题管理器
 */
class ThemeManager {
  constructor() {
    this.currentTheme = Utils.Storage.load('theme', 'light');
    this.themeToggle = document.getElementById('theme-toggle');
    this.init();
  }

  init() {
    this.applyTheme(this.currentTheme);
    
    if (this.themeToggle) {
      this.themeToggle.addEventListener('click', () => {
        this.toggleTheme();
      });
    }
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  setTheme(theme) {
    this.currentTheme = theme;
    this.applyTheme(theme);
    Utils.Storage.save('theme', theme);
  }

  applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    
    // 更新主题切换按钮图标
    if (this.themeToggle) {
      const icon = this.themeToggle.querySelector('.material-icons');
      if (icon) {
        icon.textContent = theme === 'light' ? 'dark_mode' : 'light_mode';
      }
    }
  }
}

/**
 * 文章列表管理器
 */
class ArticleListManager {
  constructor() {
    this.articles = [];
    this.filteredArticles = [];
    this.selectedArticles = [];
    this.init();
  }

  init() {
    this.setupEventListeners();
  }

  setupEventListeners() {
    // 搜索功能
    const searchInput = document.getElementById('article-search');
    if (searchInput) {
      searchInput.addEventListener('input', Utils.Event.debounce((e) => {
        this.filterArticles(e.target.value);
      }, 300));
    }

    // 全选和取消全选
    const selectAllBtn = document.getElementById('select-all-articles');
    const deselectAllBtn = document.getElementById('deselect-all-articles');
    
    if (selectAllBtn) {
      selectAllBtn.addEventListener('click', () => this.selectAll());
    }
    
    if (deselectAllBtn) {
      deselectAllBtn.addEventListener('click', () => this.deselectAll());
    }
  }

  setArticles(articles) {
    this.articles = articles;
    this.filteredArticles = [...articles];
    this.renderArticles();
  }

  addArticles(newArticles) {
    this.articles.push(...newArticles);
    this.filterArticles(document.getElementById('article-search')?.value || '');
  }

  clearArticles() {
    this.articles = [];
    this.filteredArticles = [];
    this.selectedArticles = [];
    this.renderEmptyState();
  }

  filterArticles(searchTerm = '') {
    if (!searchTerm.trim()) {
      this.filteredArticles = [...this.articles];
    } else {
      const term = searchTerm.toLowerCase();
      this.filteredArticles = this.articles.filter(article =>
        article.title.toLowerCase().includes(term) ||
        article.raw_title?.toLowerCase().includes(term) ||
        article.origin.toLowerCase().includes(term) ||
        (article.tag && article.tag.toLowerCase().includes(term))
      );
    }
    this.renderArticles();
  }

  renderArticles() {
    const articlesList = document.getElementById('articles-list');
    if (!articlesList) return;

    if (this.filteredArticles.length === 0) {
      if (this.articles.length === 0) {
        this.renderEmptyState();
      } else {
        articlesList.innerHTML = `
          <div class="empty-articles">
            <i class="material-icons">search_off</i>
            <div class="empty-text">未找到匹配的文章</div>
            <div class="empty-hint">请尝试其他搜索词</div>
          </div>
        `;
      }
      return;
    }

    // 创建表格结构
    articlesList.innerHTML = `
      <table class="articles-table">
        <thead>
          <tr>
            <th class="checkbox-col">
              <input type="checkbox" id="select-all-checkbox" class="table-checkbox">
            </th>
            <th class="title-col">文章标题</th>
            <th class="raw-title-col">原文标题</th>
            <th class="date-col">时间</th>
            <th class="tag-col">标签</th>
            <th class="origin-col">来源</th>
          </tr>
        </thead>
        <tbody class="articles-tbody">
        </tbody>
      </table>
    `;
    
    const tbody = articlesList.querySelector('.articles-tbody');
    this.filteredArticles.forEach((article, index) => {
      const articleRow = this.createArticleItem(article, index);
      tbody.appendChild(articleRow);
    });

    // 绑定全选复选框事件
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (selectAllCheckbox) {
      selectAllCheckbox.addEventListener('change', (e) => {
        if (e.target.checked) {
          this.selectAll();
        } else {
          this.deselectAll();
        }
      });
    }

    this.updateSelectionCount();
    this.updateArticleStats();
  }

  renderEmptyState() {
    const articlesList = document.getElementById('articles-list');
    if (!articlesList) return;

    articlesList.innerHTML = `
      <div class="empty-articles">
        <i class="material-icons">article</i>
        <div class="empty-text">暂无文章</div>
        <div class="empty-hint">请先在设置中配置数据源，然后开始获取文章</div>
      </div>
    `;
    this.updateArticleStats();
  }

  createArticleItem(article, index) {
    const row = Utils.DOM.createElement('tr', 'article-row');
    row.dataset.index = index;

    row.innerHTML = `
      <td class="checkbox-cell">
        <input type="checkbox" class="article-checkbox table-checkbox" id="article-${index}">
      </td>
      <td class="title-cell">
        <div class="article-title">${Utils.Text.escapeHtml(article.title)}</div>
      </td>
      <td class="raw-title-cell">
        <div class="article-raw-title">${Utils.Text.escapeHtml(article.raw_title || '-')}</div>
      </td>
      <td class="date-cell">
        <div class="article-date">${Utils.Text.escapeHtml(article.date)}</div>
      </td>
      <td class="tag-cell">
        <div class="article-tag">${Utils.Text.escapeHtml(article.tag || '未分类')}</div>
      </td>
      <td class="origin-cell">
        <div class="article-origin">${Utils.Text.escapeHtml(article.origin)}</div>
      </td>
    `;

    // 点击行选中/取消选中
    row.addEventListener('click', (e) => {
      if (e.target.type !== 'checkbox') {
        const checkbox = row.querySelector('.article-checkbox');
        checkbox.checked = !checkbox.checked;
        this.toggleArticleSelection(row, checkbox.checked);
      }
    });

    // 复选框变化事件
    const checkbox = row.querySelector('.article-checkbox');
    checkbox.addEventListener('change', (e) => {
      this.toggleArticleSelection(row, e.target.checked);
      e.stopPropagation();
    });

    return row;
  }

  toggleArticleSelection(row, selected) {
    if (selected) {
      row.classList.add('selected');
    } else {
      row.classList.remove('selected');
    }
    this.updateSelectionCount();
  }

  selectAll() {
    document.querySelectorAll('.article-checkbox').forEach(checkbox => {
      checkbox.checked = true;
      const row = checkbox.closest('.article-row');
      if (row) {
        row.classList.add('selected');
      }
    });
    this.updateSelectionCount();
  }

  deselectAll() {
    document.querySelectorAll('.article-checkbox').forEach(checkbox => {
      checkbox.checked = false;
      const row = checkbox.closest('.article-row');
      if (row) {
        row.classList.remove('selected');
      }
    });
    this.updateSelectionCount();
  }

  getSelectedArticles() {
    const selectedArticles = [];
    document.querySelectorAll('.article-checkbox:checked').forEach(checkbox => {
      const item = checkbox.closest('.article-row');
      const index = parseInt(item.dataset.index);
      selectedArticles.push(this.filteredArticles[index]);
    });
    return selectedArticles;
  }

  updateSelectionCount() {
    const selectedCount = document.querySelectorAll('.article-checkbox:checked').length;
    const totalCheckboxes = document.querySelectorAll('.article-checkbox').length;
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    
    // 更新选择数量显示
    const countElement = document.getElementById('selected-count');
    if (countElement) {
      countElement.textContent = selectedCount;
    }

    // 更新全选复选框状态
    if (selectAllCheckbox) {
      if (selectedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
      } else if (selectedCount === totalCheckboxes) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
      } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
      }
    }

    // 更新开始处理按钮状态
    const startBtn = document.getElementById('start-processing');
    if (startBtn && window.app) {
      const hasSelected = selectedCount > 0;
      const hasValidInput = window.app.state.urlText && window.app.state.urlText.trim().length > 0;
      
      startBtn.disabled = window.app.state.isProcessing || !hasValidInput || !hasSelected;
    }

    this.updateArticleStats();
  }

  updateArticleStats() {
    // 更新标题显示文章总数
    const sectionTitle = document.querySelector('.articles-section .section-title');
    if (sectionTitle) {
      const totalCount = this.articles.length;
      const filteredCount = this.filteredArticles.length;
      
      if (totalCount > 0) {
        if (filteredCount === totalCount) {
          sectionTitle.innerHTML = `
            <i class="material-icons">article</i>
            文章列表 (${totalCount} 篇)
          `;
        } else {
          sectionTitle.innerHTML = `
            <i class="material-icons">article</i>
            文章列表 (${filteredCount}/${totalCount} 篇)
          `;
        }
      } else {
        sectionTitle.innerHTML = `
          <i class="material-icons">article</i>
          文章列表
        `;
      }
    }
  }
}

// 初始化组件管理器
window.Components = {
  Modal: new ModalManager(),
  Progress: new ProgressManager(),
  Log: new LogManager(),
  Theme: new ThemeManager(),
  ArticleList: new ArticleListManager()
}; 